{"translation": {"agents": {"add.button": "Add to Assistant", "add.knowledge_base": "Knowledge Base", "add.knowledge_base.placeholder": "Select Knowledge Base", "add.name": "Name", "add.name.placeholder": "Enter name", "add.prompt": "Prompt", "add.prompt.placeholder": "Enter prompt", "add.prompt.variables.tip": {"content": "{{date}}:\tDate\n{{time}}:\tTime\n{{datetime}}:\tDate and time\n{{system}}:\tOperating system\n{{arch}}:\tCPU architecture\n{{language}}:\tLanguage\n{{model_name}}:\tModel name\n{{username}}:\tUsername", "title": "Available variables"}, "add.title": "Create Agent", "add.unsaved_changes_warning": "You have unsaved changes. Are you sure you want to close?", "delete.popup.content": "Are you sure you want to delete this agent?", "edit.model.select.title": "Select Model", "edit.title": "Edit Agent", "export": {"agent": "Export Agent"}, "import": {"button": "Import", "error": {"fetch_failed": "Failed to fetch from URL", "invalid_format": "Invalid agent format: missing required fields", "url_required": "Please enter a URL"}, "file_filter": "JSON Files", "select_file": "Select File", "title": "Import from External", "type": {"file": "File", "url": "URL"}, "url_placeholder": "Enter JSON URL"}, "manage.title": "Manage Agents", "my_agents": "My Agents", "search.no_results": "No results found", "settings": {"title": "Agent Setting"}, "sorting.title": "Sorting", "tag.agent": "Agent", "tag.default": "<PERSON><PERSON><PERSON>", "tag.new": "New", "tag.system": "System", "title": "Agents"}, "assistants": {"abbr": "Assistants", "clear.content": "Clearing the topic will delete all topics and files in the assistant. Are you sure you want to continue?", "clear.title": "Clear topics", "copy.title": "Copy Assistant", "delete.content": "Deleting an assistant will delete all topics and files under the assistant. Are you sure you want to delete it?", "delete.title": "Delete Assistant", "edit.title": "Edit Assistant", "icon.type": "Assistant <PERSON><PERSON>", "list": {"showByList": "List View", "showByTags": "Tag View"}, "save.success": "Saved successfully", "save.title": "Save to agent", "search": "Search assistants...", "settings.default_model": "Default Model", "settings.knowledge_base": "Knowledge Base Settings", "settings.knowledge_base.recognition": "Use Knowledge Base", "settings.knowledge_base.recognition.off": "Force Search", "settings.knowledge_base.recognition.on": "Intent Recognition", "settings.knowledge_base.recognition.tip": "The assistant will use the large model's intent recognition capability to determine whether to use the knowledge base for answering. This feature will depend on the model's capabilities", "settings.mcp": "MCP Servers", "settings.mcp.description": "Default enabled MCP servers", "settings.mcp.enableFirst": "Enable this server in MCP settings first", "settings.mcp.noServersAvailable": "No MCP servers available. Add servers in settings", "settings.mcp.title": "MCP Settings", "settings.model": "Model Settings", "settings.more": "Assistant Settings", "settings.prompt": "Prompt Settings", "settings.reasoning_effort": "Reasoning effort", "settings.reasoning_effort.default": "<PERSON><PERSON><PERSON>", "settings.reasoning_effort.high": "Think harder", "settings.reasoning_effort.low": "Think less", "settings.reasoning_effort.medium": "Think normally", "settings.reasoning_effort.off": "Off", "settings.regular_phrases": {"add": "Add Phrase", "contentLabel": "Content", "contentPlaceholder": "Please enter phrase content, support using variables, and press Tab to quickly locate the variable to modify. For example: \nHelp me plan a route from ${from} to ${to}, and send it to ${email}.", "delete": "Delete Phrase", "deleteConfirm": "Are you sure to delete this phrase?", "edit": "Edit Phrase", "title": "Regular Phrase", "titleLabel": "Title", "titlePlaceholder": "Enter title"}, "settings.title": "Assistant Settings", "settings.tool_use_mode": "Tool Use Mode", "settings.tool_use_mode.function": "Function", "settings.tool_use_mode.prompt": "Prompt", "tags": {"add": "Add Tag", "delete": "Delete Tag", "deleteConfirm": "Are you sure to delete this tag?", "manage": "Tag Management", "modify": "Modify Tag", "none": "No tags", "settings": {"title": "Tag Settings"}, "untagged": "Untagged"}, "title": "Assistants"}, "auth": {"error": "API key automatically obtained failed, please get it manually", "get_key": "Get", "get_key_success": "API key automatically obtained successfully", "login": "<PERSON><PERSON>", "oauth_button": "Auth with {{provider}}"}, "backup": {"confirm": "Are you sure you want to backup data?", "confirm.button": "Select Backup Location", "content": "Backup all data, including chat history, settings, and knowledge base. Please note that the backup process may take some time, thank you for your patience.", "progress": {"completed": "Backup completed", "compressing": "Compressing files...", "copying_files": "Copying files... {{progress}}%", "preparing": "Preparing backup...", "title": "Backup Progress", "writing_data": "Writing data..."}, "title": "Data Backup"}, "button": {"add": "Add", "added": "Added", "case_sensitive": "Case Sensitive", "collapse": "Collapse", "includes_user_questions": "Include Your Questions", "manage": "Manage", "select_model": "Select Model", "show.all": "Show All", "update_available": "Update Available", "whole_word": "Whole Word"}, "chat": {"add.assistant.title": "Add Assistant", "add.topic.title": "New Topic", "artifacts.button.download": "Download", "artifacts.button.openExternal": "Open in external browser", "artifacts.button.preview": "Preview", "artifacts.preview.openExternal.error.content": "Error opening the external browser.", "assistant.search.placeholder": "Search", "deeply_thought": "Deeply thought ({{seconds}} seconds)", "default.description": "Hello, I'm <PERSON><PERSON><PERSON> Assistant. You can start chatting with me right away", "default.name": "Default Assistant", "default.topic.name": "<PERSON><PERSON><PERSON>", "history": {"assistant_node": "Assistant", "click_to_navigate": "Click to navigate to the message", "coming_soon": "Chat workflow diagram coming soon", "no_messages": "No Messages Found", "start_conversation": "Start a conversation to see the chat flow diagram", "title": "Chat History", "user_node": "User", "view_full_content": "View Full Content"}, "input.auto_resize": "Auto resize height", "input.clear": "Clear {{Command}}", "input.clear.content": "Do you want to clear all messages of the current topic?", "input.clear.title": "Clear all messages?", "input.collapse": "Collapse", "input.context_count.tip": "Context / Max Context", "input.estimated_tokens.tip": "Estimated tokens", "input.expand": "Expand", "input.file_error": "Error processing file", "input.file_not_supported": "Model does not support this file type", "input.generate_image": "Generate image", "input.generate_image_not_supported": "The model does not support generating images.", "input.knowledge_base": "Knowledge Base", "input.new.context": "Clear Context {{Command}}", "input.new_topic": "New Topic {{Command}}", "input.pause": "Pause", "input.placeholder": "Type your message here, press {{key}} to send...", "input.send": "Send", "input.settings": "Settings", "input.thinking": "Thinking", "input.thinking.budget_exceeds_max": "Thinking budget exceeds the maximum token number", "input.thinking.mode.custom": "Custom", "input.thinking.mode.custom.tip": "The maximum number of tokens the model can think. Need to consider the context limit of the model, otherwise an error will be reported", "input.thinking.mode.default": "<PERSON><PERSON><PERSON>", "input.thinking.mode.default.tip": "The model will automatically determine the number of tokens to think", "input.thinking.mode.tokens.tip": "Set the number of thinking tokens to use.", "input.tools.collapse": "Collapse", "input.tools.collapse_in": "Collapse", "input.tools.collapse_out": "Remove from collapse", "input.tools.expand": "Expand", "input.topics": " Topics ", "input.translate": "Translate to {{target_language}}", "input.translating": "Translating...", "input.upload": "Upload image or document file", "input.upload.document": "Upload document file (model does not support images)", "input.upload.upload_from_local": "Upload local file...", "input.url_context": "URL Context", "input.web_search": "Web Search", "input.web_search.builtin": "Model Built-in", "input.web_search.builtin.disabled_content": "The current model does not support web search", "input.web_search.builtin.enabled_content": "Use the built-in web search function of the model", "input.web_search.button.ok": "Go to Settings", "input.web_search.enable": "Enable web search", "input.web_search.enable_content": "Need to check web search connectivity in settings first", "input.web_search.no_web_search": "Disable Web Search", "input.web_search.no_web_search.description": "Do not enable web search", "input.web_search.settings": "Web Search Settings", "message.new.branch": "New Branch", "message.new.branch.created": "New Branch Created", "message.new.context": "New Context", "message.quote": "Quote", "message.regenerate.model": "Switch Model", "message.useful": "Helpful", "multiple.select": "Multiple Select", "multiple.select.empty": "No Messages Selected", "navigation": {"bottom": "Back to bottom", "close": "Close", "first": "Already at the first message", "history": "Chat History", "last": "Already at the last message", "next": "Next Message", "prev": "Previous Message", "top": "Back to top"}, "resend": "Resend", "save": "Save", "save.file.title": "Save to Local File", "save.knowledge": {"content.citation.description": "Includes web search and knowledge base reference information", "content.citation.title": "Citations", "content.code.description": "Includes standalone code blocks", "content.code.title": "Code Blocks", "content.error.description": "Includes error messages during execution", "content.error.title": "Errors", "content.file.description": "Includes attached files", "content.file.title": "Files", "content.maintext.description": "Includes primary text content", "content.maintext.title": "Main Text", "content.thinking.description": "Includes model reasoning content", "content.thinking.title": "Reasoning", "content.tool_use.description": "Includes tool call parameters and execution results", "content.tool_use.title": "Tool Usage", "content.translation.description": "Includes translation content", "content.translation.title": "Translations", "empty.no_content": "This message has no saveable content", "empty.no_knowledge_base": "No knowledge bases available, please create one first", "error.invalid_base": "Selected knowledge base is not properly configured", "error.no_content_selected": "Please select at least one content type", "error.save_failed": "Save failed, please check knowledge base configuration", "select.base.placeholder": "Please select a knowledge base", "select.base.title": "Select Knowledge Base", "select.content.tip": "Selected {{count}} items, text types will be merged and saved as one note", "select.content.title": "Select content types to save", "title": "Save to Knowledge Base"}, "settings.code.title": "Code Block Settings", "settings.code_collapsible": "Code block collapsible", "settings.code_editor": {"autocompletion": "Autocompletion", "fold_gutter": "Fold gutter", "highlight_active_line": "Highlight active line", "keymap": "Keymap", "title": "Code Editor"}, "settings.code_execution": {"timeout_minutes": "Timeout", "timeout_minutes.tip": "The timeout time (minutes) of code execution", "tip": "The run button will be displayed in the toolbar of executable code blocks, please do not execute dangerous code!", "title": "Code Execution"}, "settings.code_wrappable": "Code block wrappable", "settings.context_count": "Context", "settings.context_count.tip": "The number of previous messages to keep in the context.", "settings.max": "Max", "settings.max_tokens": "Set max tokens", "settings.max_tokens.confirm": "Set max tokens", "settings.max_tokens.confirm_content": "Set the maximum number of tokens the model can generate. Need to consider the context limit of the model, otherwise an error will be reported", "settings.max_tokens.tip": "The maximum number of tokens the model can generate. Need to consider the context limit of the model, otherwise an error will be reported", "settings.reset": "Reset", "settings.set_as_default": "Apply to default assistant", "settings.show_line_numbers": "Show line numbers in code", "settings.temperature": "Temperature", "settings.temperature.tip": "Higher values make the model more creative and unpredictable, while lower values make it more deterministic and precise.", "settings.thought_auto_collapse": "Collapse Thought Content", "settings.thought_auto_collapse.tip": "Automatically collapse thought content after thinking ends", "settings.top_p": "Top-P", "settings.top_p.tip": "Default value is 1, the smaller the value, the less variety in the answers, the easier to understand, the larger the value, the larger the range of the AI's vocabulary, the more diverse", "suggestions.title": "Suggested Questions", "thinking": "Thinking ({{seconds}} seconds)", "topics.auto_rename": "Auto Rename", "topics.clear.title": "Clear Messages", "topics.copy.image": "Copy as image", "topics.copy.md": "<PERSON><PERSON> as markdown", "topics.copy.plain_text": "Copy as plain text (remove Markdown)", "topics.copy.title": "Copy", "topics.delete.shortcut": "Hold {{key}} to delete directly", "topics.edit.placeholder": "Enter new name", "topics.edit.title": "Edit Name", "topics.export.image": "Export as image", "topics.export.joplin": "Export to <PERSON><PERSON><PERSON>", "topics.export.md": "Export as markdown", "topics.export.md.reason": "Export as Mark<PERSON> (with reasoning)", "topics.export.notion": "Export to Notion", "topics.export.obsidian": "Export to Obsidian", "topics.export.obsidian_atributes": "Configure Note Attributes", "topics.export.obsidian_btn": "Confirm", "topics.export.obsidian_created": "Creation Time", "topics.export.obsidian_created_placeholder": "Please select the creation time", "topics.export.obsidian_export_failed": "Export failed", "topics.export.obsidian_export_success": "Export success", "topics.export.obsidian_fetch_error": "Failed to fetch Obsidian vaults", "topics.export.obsidian_fetch_folders_error": "Failed to fetch folder structure", "topics.export.obsidian_loading": "Loading...", "topics.export.obsidian_no_vault_selected": "Please select a vault first", "topics.export.obsidian_no_vaults": "No Obsidian vaults found", "topics.export.obsidian_operate": "Operation Method", "topics.export.obsidian_operate_append": "Append", "topics.export.obsidian_operate_new_or_overwrite": "Create New (Overwrite if it exists)", "topics.export.obsidian_operate_placeholder": "Please select the operation method", "topics.export.obsidian_operate_prepend": "Prepend", "topics.export.obsidian_path": "Path", "topics.export.obsidian_path_placeholder": "Please select the path", "topics.export.obsidian_reasoning": "Include Reasoning Chain", "topics.export.obsidian_root_directory": "Root Directory", "topics.export.obsidian_select_vault_first": "Please select a vault first", "topics.export.obsidian_source": "Source", "topics.export.obsidian_source_placeholder": "Please enter the source", "topics.export.obsidian_tags": "Tags", "topics.export.obsidian_tags_placeholder": "Please enter tags, separate multiple tags with commas", "topics.export.obsidian_title": "Title", "topics.export.obsidian_title_placeholder": "Please enter the title", "topics.export.obsidian_title_required": "The title cannot be empty", "topics.export.obsidian_vault": "<PERSON><PERSON>", "topics.export.obsidian_vault_placeholder": "Please select the vault name", "topics.export.siyuan": "Export to Siyuan Note", "topics.export.title": "Export", "topics.export.title_naming_failed": "Failed to generate title, using default title", "topics.export.title_naming_success": "Title generated successfully", "topics.export.wait_for_title_naming": "Generating title...", "topics.export.word": "Export as Word", "topics.export.yuque": "Export to Yuque", "topics.list": "Topic List", "topics.move_to": "Move to", "topics.new": "New Topic", "topics.pinned": "Pinned Topics", "topics.prompt": "Topic Prompts", "topics.prompt.edit.title": "Edit Topic Prompts", "topics.prompt.tips": "Topic Prompts: Additional supplementary prompts provided for the current topic", "topics.title": "Topics", "topics.unpinned": "Unpinned Topics", "translate": "Translate"}, "code_block": {"collapse": "Collapse", "copy": "Copy", "copy.failed": "Co<PERSON> failed", "copy.source": "Copy Source Code", "copy.success": "<PERSON>pied", "download": "Download", "download.failed.network": "Download failed, please check the network", "download.png": "Download PNG", "download.source": "Download Source Code", "download.svg": "Download SVG", "edit": "Edit", "edit.save": "Save Changes", "edit.save.failed": "Save failed", "edit.save.failed.message_not_found": "Save failed, message not found", "edit.save.success": "Saved", "expand": "Expand", "more": "More", "preview": "Preview", "preview.copy.image": "Copy as image", "preview.source": "View Source Code", "preview.zoom_in": "Zoom In", "preview.zoom_out": "Zoom Out", "run": "Run", "split": "Split View", "split.restore": "Restore Split View", "wrap.off": "Unwrap", "wrap.on": "Wrap"}, "common": {"add": "Add", "advanced_settings": "Advanced Settings", "and": "and", "assistant": "Assistant", "avatar": "Avatar", "back": "Back", "browse": "Browse", "cancel": "Cancel", "chat": "Cha<PERSON>", "clear": "Clear", "close": "Close", "collapse": "Collapse", "confirm": "Confirm", "copied": "<PERSON>pied", "copy": "Copy", "copy_failed": "Co<PERSON> failed", "cut": "Cut", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "delete_confirm": "Are you sure you want to delete?", "description": "Description", "disabled": "Disabled", "docs": "Docs", "download": "Download", "duplicate": "Duplicate", "edit": "Edit", "enabled": "Enabled", "expand": "Expand", "footnote": "Reference content", "footnotes": "References", "fullscreen": "Entered fullscreen mode. Press F11 to exit", "i_know": "I know", "inspect": "Inspect", "knowledge_base": "Knowledge Base", "language": "Language", "loading": "Loading...", "model": "Model", "models": "Models", "more": "More", "name": "Name", "no_results": "No results", "open": "Open", "paste": "Paste", "prompt": "Prompt", "provider": "Provider", "reasoning_content": "Deep reasoning", "refresh": "Refresh", "regenerate": "Regenerate", "rename": "<PERSON><PERSON>", "reset": "Reset", "save": "Save", "search": "Search", "select": "Select", "selectedItems": "Selected {{count}} items", "selectedMessages": "Selected {{count}} messages", "settings": "Settings", "sort": {"pinyin": "Sort by <PERSON><PERSON><PERSON>", "pinyin.asc": "Sort by <PERSON><PERSON><PERSON> (A-Z)", "pinyin.desc": "Sort by <PERSON><PERSON><PERSON> (Z-A)"}, "success": "Success", "swap": "<PERSON><PERSON><PERSON>", "topics": "Topics", "warning": "Warning", "you": "You"}, "docs": {"title": "Docs"}, "endpoint_type": {"anthropic": "Anthropic", "gemini": "Gemini", "image-generation": "Image Generation", "jina-rerank": "<PERSON><PERSON>", "openai": "OpenAI", "openai-response": "OpenAI-Response"}, "error": {"backup.file_format": "Backup file format error", "chat.response": "Something went wrong. Please check if you have set your API key in the Settings > Providers", "http": {"400": "Request failed. Please check if the request parameters are correct. If you have changed the model settings, please reset them to the default settings", "401": "Authentication failed. Please check if your API key is correct", "403": "Access denied. Please check if your account is verified, or contact the service provider for more information", "404": "Model not found or request path is incorrect", "429": "Too many requests. Please try again later", "500": "Server error. Please try again later", "502": "Gateway error. Please try again later", "503": "Service unavailable. Please try again later", "504": "Gateway timeout. Please try again later"}, "missing_user_message": "Cannot switch model response: The original user message has been deleted. Please send a new message to get a response with this model.", "model.exists": "Model already exists", "no_api_key": "API key is not configured", "pause_placeholder": "Paused", "provider_disabled": "Model provider is not enabled", "render": {"description": "Failed to render message content. Please check if the message content format is correct", "title": "Render Error"}, "unknown": "Unknown error", "user_message_not_found": "Cannot find original user message to resend"}, "export": {"assistant": "Assistant", "attached_files": "Attached Files", "conversation_details": "Conversation Details", "conversation_history": "Conversation History", "created": "Created", "last_updated": "Last Updated", "messages": "Messages", "user": "User"}, "files": {"actions": "Actions", "all": "All Files", "count": "files", "created_at": "Created At", "delete": "Delete", "delete.content": "Deleting a file will delete its reference from all messages. Are you sure you want to delete this file?", "delete.paintings.warning": "Image contains this file, deletion is not possible", "delete.title": "Delete File", "document": "Document", "edit": "Edit", "file": "File", "image": "Image", "name": "Name", "open": "Open", "size": "Size", "text": "Text", "title": "Files", "type": "Type"}, "gpustack": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "GPUStack"}, "history": {"continue_chat": "Continue <PERSON>", "locate.message": "Locate the message", "search.messages": "Search All Messages", "search.placeholder": "Search topics or messages...", "search.topics.empty": "No topics found, press Enter to search all messages", "title": "Topics Search"}, "html_artifacts": {"code": "Code", "generating": "Generating", "preview": "Preview", "split": "Split"}, "knowledge": {"add": {"title": "Add Knowledge Base"}, "add_directory": "Add Directory", "add_file": "Add File", "add_note": "Add Note", "add_sitemap": "Website Map", "add_url": "Add URL", "cancel_index": "Cancel Indexing", "chunk_overlap": "<PERSON><PERSON>", "chunk_overlap_placeholder": "Default (not recommended to change)", "chunk_overlap_tooltip": "The amount of duplicate content between adjacent chunks, ensuring that the chunks are still contextually related, improving the overall effect of processing long text", "chunk_size": "Chunk Size", "chunk_size_change_warning": "Chunk size and overlap size changes only apply to new content", "chunk_size_placeholder": "Default (not recommended to change)", "chunk_size_too_large": "Chunk size cannot exceed model context limit ({{max_context}})", "chunk_size_tooltip": "Split documents into chunks, each chunk size, not exceeding model context limit", "clear_selection": "Clear selection", "delete": "Delete", "delete_confirm": "Are you sure you want to delete this knowledge base?", "dimensions": "Embedding dimension", "dimensions_auto_set": "Auto-set embedding dimensions", "dimensions_default": "The model will use default embedding dimensions", "dimensions_error_invalid": "Please enter embedding dimension size", "dimensions_set_right": "⚠️ Please ensure the model supports the set embedding dimension size", "dimensions_size_placeholder": " Embedding dimension size, e.g. 1024", "dimensions_size_too_large": "The embedding dimension cannot exceed the model's context limit ({{max_context}}).", "dimensions_size_tooltip": "The size of the embedding dimension; the larger the value, the larger the embedding dimension, but it also consumes more tokens.", "directories": "Directories", "directory_placeholder": "Enter Directory Path", "document_count": "Requested Document Chunks", "document_count_default": "<PERSON><PERSON><PERSON>", "document_count_help": "The more document chunks requested, the more information is included, but the more tokens are consumed", "drag_file": "Drag file here", "edit_remark": "Edit Remark", "edit_remark_placeholder": "Please enter remark content", "embedding_model_required": "Knowledge Base Embedding Model is required", "empty": "No knowledge base found", "file_hint": "Support {{file_types}}", "index_all": "Index All", "index_cancelled": "Indexing cancelled", "index_started": "Indexing started", "invalid_url": "Invalid URL", "model_info": "Model Info", "name_required": "Knowledge Base Name is required", "no_bases": "No knowledge bases available", "no_match": "No matching content found in the knowledge base.", "no_provider": "Knowledge base model provider is not set, the knowledge base will no longer be supported, please create a new knowledge base", "not_set": "Not Set", "not_support": "Knowledge base database engine updated, the knowledge base will no longer be supported, please create a new knowledge base", "notes": "Notes", "notes_placeholder": "Enter additional information or context for this knowledge base...", "quota": "{{name}} Left Quota: {{quota}}", "quota_infinity": "{{name}} Quota: Unlimited", "rename": "<PERSON><PERSON>", "search": "Search knowledge base", "search_placeholder": "Enter text to search", "settings": {"preprocessing": "Preprocessing", "preprocessing_tooltip": "Preprocess uploaded files with OCR", "title": "Knowledge Base Settings"}, "sitemap_placeholder": "Enter Website Map URL", "sitemaps": "Websites", "source": "Source", "status": "Status", "status_completed": "Completed", "status_embedding_completed": "Embedding Completed", "status_embedding_failed": "Embedding Failed", "status_failed": "Failed", "status_new": "Added", "status_pending": "Pending", "status_preprocess_completed": "Preprocessing Completed", "status_preprocess_failed": "Preprocessing Failed", "status_processing": "Processing", "threshold": "Matching threshold", "threshold_placeholder": "Not set", "threshold_too_large_or_small": "Threshold cannot be greater than 1 or less than 0", "threshold_tooltip": "Used to evaluate the relevance between the user's question and the content in the knowledge base (0-1)", "title": "Knowledge Base", "topN": "Number of results returned", "topN_placeholder": "Not set", "topN_too_large_or_small": "The number of results returned cannot be greater than 30 or less than 1.", "topN_tooltip": "The number of matching results returned; the larger the value, the more matching results, but also the more tokens consumed.", "url_added": "URL added", "url_placeholder": "Enter URL, multiple URLs separated by Enter", "urls": "URLs"}, "languages": {"arabic": "Arabic", "chinese": "Chinese", "chinese-traditional": "Traditional Chinese", "english": "English", "french": "French", "german": "German", "indonesian": "Indonesian", "italian": "Italian", "japanese": "Japanese", "korean": "Korean", "malay": "Malay", "polish": "Polish", "portuguese": "Portuguese", "russian": "Russian", "spanish": "Spanish", "thai": "Thai", "turkish": "Turkish", "urdu": "Urdu", "vietnamese": "Vietnamese"}, "launchpad": {"apps": "Apps", "minapps": "<PERSON><PERSON>"}, "lmstudio": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "LM Studio"}, "memory": {"actions": "Actions", "add_failed": "Failed to add memory", "add_first_memory": "Add Your First Memory", "add_memory": "Add Memory", "add_new_user": "Add New User", "add_success": "Memory added successfully", "add_user": "Add User", "add_user_failed": "Failed to add user", "all_users": "All Users", "cannot_delete_default_user": "Cannot delete the default user", "configure_memory_first": "Please configure memory settings first", "content": "Content", "current_user": "Current User", "custom": "Custom", "default": "<PERSON><PERSON><PERSON>", "default_user": "Default User", "delete_confirm": "Are you sure you want to delete this memory?", "delete_confirm_content": "Are you sure you want to delete {{count}} memories?", "delete_confirm_single": "Are you sure you want to delete this memory?", "delete_confirm_title": "Delete Memories", "delete_failed": "Failed to delete memory", "delete_selected": "Delete Selected", "delete_success": "Memory deleted successfully", "delete_user": "Delete User", "delete_user_confirm_content": "Are you sure you want to delete user {{user}} and all their memories?", "delete_user_confirm_title": "Delete User", "delete_user_failed": "Failed to delete user", "description": "Memory allows you to store and manage information about your interactions with the assistant. You can add, edit, and delete memories, as well as filter and search through them.", "edit_memory": "Edit Memory", "embedding_dimensions": "Embedding Dimensions", "embedding_model": "Embedding Model", "enable_global_memory_first": "Please enable global memory first", "end_date": "End Date", "global_memory": "Global Memory", "global_memory_description": "To use memory features, please enable global memory in assistant settings.", "global_memory_disabled_desc": "To use memory features, please enable global memory in assistant settings first.", "global_memory_disabled_title": "Global Memory Disabled", "global_memory_enabled": "Global memory enabled", "go_to_memory_page": "Go to Memory Page", "initial_memory_content": "Welcome! This is your first memory.", "llm_model": "LLM Model", "load_failed": "Failed to load memories", "loading": "Loading memories...", "loading_memories": "Loading memories...", "memories_description": "Showing {{count}} of {{total}} memories", "memories_reset_success": "All memories for {{user}} have been reset successfully", "memory": "memory", "memory_content": "Memory Content", "memory_placeholder": "Enter memory content...", "new_user_id": "New User ID", "new_user_id_placeholder": "Enter a unique user ID", "no_matching_memories": "No matching memories found", "no_memories": "No memories yet", "no_memories_description": "Start by adding your first memory to get started", "not_configured_desc": "Please configure embedding and LLM models in memory settings to enable memory functionality.", "not_configured_title": "Memory Not Configured", "pagination_total": "{{start}}-{{end}} of {{total}} items", "please_enter_memory": "Please enter memory content", "please_select_embedding_model": "Please select an embedding model", "please_select_llm_model": "Please select an LLM model", "reset_filters": "Reset Filters", "reset_memories": "Reset Memories", "reset_memories_confirm_content": "Are you sure you want to permanently delete all memories for {{user}}? This action cannot be undone.", "reset_memories_confirm_title": "Reset All Memories", "reset_memories_failed": "Failed to reset memories", "reset_user_memories": "Reset User Memories", "reset_user_memories_confirm_content": "Are you sure you want to reset all memories for {{user}}?", "reset_user_memories_confirm_title": "Reset User Memories", "reset_user_memories_failed": "Failed to reset user memories", "score": "Score", "search": "Search", "search_placeholder": "Search memories...", "select_embedding_model_placeholder": "Select Embedding Model", "select_llm_model_placeholder": "Select LLM Model", "select_user": "Select User", "settings": "Settings", "settings_title": "Memory Settings", "start_date": "Start Date", "statistics": "Statistics", "stored_memories": "Stored Memories", "switch_user": "Switch User", "switch_user_confirm": "Switch user context to {{user}}?", "time": "Time", "title": "Memories", "total_memories": "total memories", "try_different_filters": "Try adjusting your search criteria", "update_failed": "Failed to update memory", "update_success": "Memory updated successfully", "user": "User", "user_created": "User {{user}} created and switched successfully", "user_deleted": "User {{user}} deleted successfully", "user_id": "User ID", "user_id_exists": "This user ID already exists", "user_id_invalid_chars": "User ID can only contain letters, numbers, hyphens and underscores", "user_id_placeholder": "Enter user ID (optional)", "user_id_required": "User ID is required", "user_id_reserved": "'default-user' is reserved, please use a different ID", "user_id_rules": "User ID must be unique and contain only letters, numbers, hyphens (-) and underscores (_)", "user_id_too_long": "User ID cannot exceed 50 characters", "user_management": "User Management", "user_memories_reset": "All memories for {{user}} have been reset", "user_switch_failed": "Failed to switch user", "user_switched": "User context switched to {{user}}", "users": "users"}, "message": {"agents": {"import.error": "Import failed", "imported": "Imported successfully"}, "api.check.model.title": "Select the model to use for detection", "api.connection.failed": "Connection failed", "api.connection.success": "Connection successful", "assistant.added.content": "Assistant added successfully", "attachments": {"pasted_image": "Pasted Image", "pasted_text": "Pasted Text"}, "backup.failed": "Backup failed", "backup.start.success": "Backup started", "backup.success": "Backup successful", "chat.completion.paused": "Chat completion paused", "citation": "{{count}} citations", "citations": "References", "copied": "Copied!", "copy.failed": "Co<PERSON> failed", "copy.success": "Copied!", "delete.confirm.content": "Are you sure you want to delete the selected {{count}} message(s)?", "delete.confirm.title": "Delete Confirmation", "delete.failed": "Delete Failed", "delete.success": "Delete Successful", "download.failed": "Download failed", "download.success": "Download successfully", "empty_url": "Failed to download image, possibly due to prompt containing sensitive content or prohibited words", "error.chunk_overlap_too_large": "Chunk overlap cannot be greater than chunk size", "error.dimension_too_large": "Content size is too large", "error.enter.api.host": "Please enter your API host first", "error.enter.api.key": "Please enter your API key first", "error.enter.model": "Please select a model first", "error.enter.name": "Please enter the name of the knowledge base", "error.fetchTopicName": "Failed to name the topic", "error.get_embedding_dimensions": "Failed to get embedding dimensions", "error.invalid.api.host": "Invalid API Host", "error.invalid.api.key": "Invalid API Key", "error.invalid.enter.model": "Please select a model", "error.invalid.nutstore": "Invalid Nutstore settings", "error.invalid.nutstore_token": "Invalid Nutstore Token", "error.invalid.proxy.url": "Invalid proxy URL", "error.invalid.webdav": "Invalid WebDAV settings", "error.joplin.export": "Failed to export to Joplin. Please keep Joplin running and check connection status or configuration", "error.joplin.no_config": "Joplin Authorization Token or URL is not configured", "error.markdown.export.preconf": "Failed to export the Markdown file to the preconfigured path", "error.markdown.export.specified": "Failed to export the Markdown file", "error.notion.export": "Failed to export to Notion. Please check connection status and configuration according to documentation", "error.notion.no_api_key": "Notion ApiKey or Notion DatabaseID is not configured", "error.siyuan.export": "Failed to export to Siyuan Note, please check connection status and configuration according to documentation", "error.siyuan.no_config": "Siyuan Note API address or token is not configured", "error.yuque.export": "Failed to export to Yuque. Please check connection status and configuration according to documentation", "error.yuque.no_config": "<PERSON><PERSON> or <PERSON><PERSON> is not configured", "group.delete.content": "Deleting a group message will delete the user's question and all assistant's answers", "group.delete.title": "Delete Group Message", "ignore.knowledge.base": "Web search mode is enabled, ignore knowledge base", "loading.notion.exporting_progress": "Exporting to Notion ...", "loading.notion.preparing": "Preparing to export to Notion...", "mention.title": "Switch model answer", "message.code_style": "Code style", "message.delete.content": "Are you sure you want to delete this message?", "message.delete.title": "Delete Message", "message.multi_model_style": "Group style", "message.multi_model_style.fold": "Fold view", "message.multi_model_style.fold.compress": "Switch to compact layout", "message.multi_model_style.fold.expand": "Switch to expanded layout", "message.multi_model_style.grid": "Grid layout", "message.multi_model_style.horizontal": "Side by side", "message.multi_model_style.vertical": "Stacked view", "message.style": "Message style", "message.style.bubble": "Bubble", "message.style.plain": "Plain", "processing": "Processing...", "regenerate.confirm": "Regenerating will replace current message", "reset.confirm.content": "Are you sure you want to clear all data?", "reset.double.confirm.content": "All data will be lost, do you want to continue?", "reset.double.confirm.title": "DATA LOST !!!", "restore.failed": "Rest<PERSON> failed", "restore.success": "Restored successfully", "save.success.title": "Saved successfully", "searching": "Searching...", "success.joplin.export": "Successfully exported to Joplin", "success.markdown.export.preconf": "Successfully exported the Markdown file to the preconfigured path", "success.markdown.export.specified": "Successfully exported the Markdown file", "success.notion.export": "Successfully exported to Notion", "success.siyuan.export": "Successfully exported to Siyuan Note", "success.yuque.export": "Successfully exported to Yuque", "switch.disabled": "Please wait for the current reply to complete", "tools": {"abort_failed": "Tool call abort failed", "aborted": "Tool call aborted", "autoApproveEnabled": "Auto-approve enabled for this tool", "cancelled": "Cancelled", "completed": "Completed", "error": "Error occurred", "invoking": "Invoking", "pending": "Pending", "preview": "Preview", "raw": "Raw"}, "topic.added": "New topic added", "upgrade.success.button": "<PERSON><PERSON>", "upgrade.success.content": "Please restart the application to complete the upgrade", "upgrade.success.title": "Upgrade successfully", "warn.notion.exporting": "Exporting to Notion, please do not request export repeatedly!", "warn.siyuan.exporting": "Exporting to Siyuan Note, please do not request export repeatedly!", "warn.yuque.exporting": "Exporting to Yuque, please do not request export repeatedly!", "warning.rate.limit": "Too many requests. Please wait {{seconds}} seconds before trying again.", "websearch": {"cutoff": "Truncating search content...", "fetch_complete": "Completed {{count}} searches...", "rag": "Executing RAG...", "rag_complete": "Keeping {{countAfter}} out of {{countBefore}} results...", "rag_failed": "RAG failed, returning empty results..."}}, "minapp": {"add_to_launchpad": "Add to Launchpad", "add_to_sidebar": "Add to Sidebar", "popup": {"close": "Close MinApp", "devtools": "Developer Tools", "goBack": "Go Back", "goForward": "Go Forward", "minimize": "Minimize MinApp", "openExternal": "Open in Browser", "open_link_external_off": "Current: Open links in default window", "open_link_external_on": "Current: Open links in browser", "refresh": "Refresh", "rightclick_copyurl": "Right-click to copy URL"}, "remove_from_launchpad": "Remove from Launchpad", "remove_from_sidebar": "Remove from Sidebar", "sidebar": {"close": {"title": "Close"}, "closeall": {"title": "Close All"}, "hide": {"title": "<PERSON>de"}, "remove_custom": {"title": "Delete Custom App"}}, "title": "MinApp"}, "miniwindow": {"alert": {"google_login": "Tip: If you see a 'browser not trusted' message when logging into Google, please first login through the Google mini app in the mini app list, then use Google login in other mini apps"}, "clipboard": {"empty": "Clipboard is empty"}, "feature": {"chat": "Answer this question", "explanation": "Explanation", "summary": "Content summary", "translate": "Text translation"}, "footer": {"backspace_clear": "Backspace to clear", "copy_last_message": "Press C to copy", "esc": "ESC to {{action}}", "esc_back": "return", "esc_close": "close", "esc_pause": "pause"}, "input": {"placeholder": {"empty": "Ask {{model}} for help...", "title": "What do you want to do with this text?"}}, "tooltip": {"pin": "Keep Window on Top"}}, "models": {"add_parameter": "Add Parameter", "all": "All", "custom_parameters": "Custom Parameters", "dimensions": "Dimensions {{dimensions}}", "edit": "Edit Model", "embedding": "Embedding", "embedding_dimensions": "Embedding Dimensions", "embedding_model": "Embedding Model", "embedding_model_tooltip": "Add in Settings->Model Provider->Manage", "enable_tool_use": "Enable Tool Use", "function_calling": "Function Calling", "no_matches": "No models available", "parameter_name": "Parameter Name", "parameter_type": {"boolean": "Boolean", "json": "JSON", "number": "Number", "string": "Text"}, "pinned": "Pinned", "price": {"cost": "Cost", "currency": "<PERSON><PERSON><PERSON><PERSON>", "custom": "Custom", "custom_currency": "Custom Currency", "custom_currency_placeholder": "Enter Custom Currency", "input": "Input Price", "million_tokens": "M <PERSON>kens", "output": "Output Price", "price": "Price"}, "reasoning": "Reasoning", "rerank_model": "<PERSON><PERSON><PERSON>", "rerank_model_not_support_provider": "Currently, the reranker model does not support this provider ({{provider}})", "rerank_model_support_provider": "Currently, the reranker model only supports some providers ({{provider}})", "rerank_model_tooltip": "Click the Manage button in Settings -> Model Services to add.", "search": "Search models...", "stream_output": "Stream output", "type": {"embedding": "Embedding", "free": "Free", "function_calling": "Tool", "reasoning": "Reasoning", "rerank": "<PERSON><PERSON><PERSON>", "select": "Select Model Types", "text": "Text", "vision": "Vision", "websearch": "WebSearch"}}, "navbar": {"expand": "Expand Dialog", "hide_sidebar": "<PERSON><PERSON>bar", "show_sidebar": "Show Sidebar"}, "notification": {"assistant": "Assistant Response", "knowledge.error": "{{error}}", "knowledge.success": "Successfully added {{type}} to the knowledge base", "tip": "If the response is successful, then only messages exceeding 30 seconds will trigger a reminder"}, "ollama": {"keep_alive_time.description": "The time in minutes to keep the connection alive, default is 5 minutes.", "keep_alive_time.placeholder": "Minutes", "keep_alive_time.title": "Keep Alive Time", "title": "Ollama"}, "paintings": {"aspect_ratio": "Aspect Ratio", "aspect_ratios": {"landscape": "Landscape", "portrait": "Portrait", "square": "Square"}, "auto_create_paint": "Auto-create image", "auto_create_paint_tip": "After the image is generated, a new image will be created automatically.", "background": "Background", "background_options": {"auto": "Auto", "opaque": "Opaque", "transparent": "Transparent"}, "button.delete.image": "Delete Image", "button.delete.image.confirm": "Are you sure you want to delete this image?", "button.new.image": "New Image", "edit": {"image_file": "Edited Image", "magic_prompt_option_tip": "Intelligently enhances editing prompts", "model_tip": "V3 and V2 versions supported", "number_images_tip": "Number of edited results to generate", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3", "seed_tip": "Controls editing randomness", "style_type_tip": "Style for edited image, only for V_2 and above"}, "generate": {"magic_prompt_option_tip": "Intelligently enhances prompts for better results", "model_tip": "Model version: V3 is the latest version, V2 is the previous model, V2A is the fast model, V_1 is the first-generation model, _TURBO is the acceleration version", "negative_prompt_tip": "Describe unwanted elements, only for V_1, V_1_TURBO, V_2, and V_2_TURBO", "number_images_tip": "Number of images to generate", "person_generation": "Generate person", "person_generation_tip": "Allow model to generate person images", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3", "seed_tip": "Controls image generation randomness for reproducible results", "style_type_tip": "Image generation style for V_2 and above"}, "generated_image": "Generated Image", "go_to_settings": "Go to Settings", "guidance_scale": "Guidance Scale", "guidance_scale_tip": "Classifier Free Guidance. How close you want the model to stick to your prompt when looking for a related image to show you", "image.size": "Image Size", "image_file_required": "Please upload an image first", "image_file_retry": "Please re-upload an image first", "image_handle_required": "Please upload an image first.", "image_placeholder": "No image available", "image_retry": "Retry", "image_size_options": {"auto": "Auto"}, "inference_steps": "Inference Steps", "inference_steps_tip": "The number of inference steps to perform. More steps produce higher quality but take longer", "input_image": "Input Image", "input_parameters": "Input Parameters", "learn_more": "Learn More", "magic_prompt_option": "Magic Prompt", "mode": {"edit": "Edit", "generate": "Draw", "remix": "Remix", "upscale": "Upscale"}, "model": "Model", "model_and_pricing": "Model & Pricing", "moderation": "Moderation", "moderation_options": {"auto": "Auto", "low": "Low"}, "negative_prompt": "Negative Prompt", "negative_prompt_tip": "Describe what you don't want included in the image", "no_image_generation_model": "No available image generation model, please add a model and set the endpoint type to {{endpoint_type}}", "number_images": "Number Images", "number_images_tip": "Number of images to generate (1-4)", "paint_course": "tutorial", "per_image": "per image", "per_images": "per images", "person_generation_options": {"allow_adult": "Allow adult", "allow_all": "Allow all", "allow_none": "Not allowed"}, "pricing": "Pricing", "prompt_enhancement": "Prompt Enhancement", "prompt_enhancement_tip": "Rewrite prompts into detailed, model-friendly versions when switched on", "prompt_placeholder": "Describe the image you want to create, e.g. A serene lake at sunset with mountains in the background", "prompt_placeholder_edit": "Enter your image description, text drawing uses \"double quotes\" to wrap", "prompt_placeholder_en": "Enter your image description, currently Imagen only supports English prompts", "proxy_required": "Open the proxy and enable \"TUN mode\" to view generated images or copy them to the browser for opening. In the future, domestic direct connection will be supported", "quality": "Quality", "quality_options": {"auto": "Auto", "high": "High", "low": "Low", "medium": "Medium"}, "regenerate.confirm": "This will replace your existing generated images. Do you want to continue?", "remix": {"image_file": "Reference Image", "image_weight": "Reference Image Weight", "image_weight_tip": "Adjust reference image influence", "magic_prompt_option_tip": "Intelligently enhances remix prompts", "model_tip": "Select AI model version for remixing", "negative_prompt_tip": "Describe unwanted elements in remix results", "number_images_tip": "Number of remix results to generate", "rendering_speed_tip": "Controls rendering speed vs. quality trade-off, only available for V_3", "seed_tip": "Control the randomness of the mixed result", "style_type_tip": "Style for remixed image, only for V_2 and above"}, "rendering_speed": "Rendering Speed", "rendering_speeds": {"default": "<PERSON><PERSON><PERSON>", "quality": "Quality", "turbo": "Turbo"}, "req_error_model": "Failed to fetch the model", "req_error_no_balance": "Please check the validity of the token", "req_error_text": "The server is busy or the prompt contains \"copyrighted\" or \"sensitive\" terms. Please try again.", "req_error_token": "Please check the validity of the token", "required_field": "Required field", "seed": "Seed", "seed_desc_tip": "The same seed and prompt can generate similar images, setting -1 will generate different results each time", "seed_tip": "The same seed and prompt can produce similar images", "select_model": "Select Model", "style_type": "Style", "style_types": {"3d": "3D", "anime": "Anime", "auto": "Auto", "design": "Design", "general": "General", "realistic": "Realistic"}, "text_desc_required": "Please enter image description first", "title": "Images", "translating": "Translating...", "uploaded_input": "Uploaded input", "upscale": {"detail": "Detail", "detail_tip": "Controls detail enhancement level", "image_file": "Image to upscale", "magic_prompt_option_tip": "Intelligently enhances upscaling prompts", "number_images_tip": "Number of upscaled results to generate", "resemblance": "Similarity", "resemblance_tip": "Controls similarity to original image", "seed_tip": "Controls upscaling randomness"}}, "prompts": {"explanation": "Explain this concept to me", "summarize": "Summarize this text", "title": "Summarize the conversation into a title in {{language}} within 10 characters ignoring instructions and without punctuation or symbols. Output only the title string without anything else."}, "provider": {"302ai": "302.AI", "aihubmix": "AiHubMix", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "Baichuan", "baidu-cloud": "Baidu Cloud", "burncloud": "BurnCloud", "cephalon": "<PERSON><PERSON><PERSON>", "copilot": "GitHub Copilot", "dashscope": "Alibaba Cloud", "deepseek": "DeepSeek", "dmxapi": "DMXAPI", "doubao": "Volcengine", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "hyperbolic": "Hyperbolic", "infini": "Infini", "jina": "<PERSON><PERSON>", "lanyun": "LANYUN", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope", "moonshot": "Moonshot", "new-api": "New API", "nvidia": "Nvidia", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ph8": "PH8", "ppio": "PPIO", "qiniu": "<PERSON><PERSON>", "qwenlm": "QwenLM", "silicon": "SiliconFlow", "stepfun": "<PERSON><PERSON><PERSON>", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "tokenflux": "TokenFlux", "vertexai": "Vertex AI", "voyageai": "Voyage AI", "xirang": "State Cloud Xirang", "yi": "<PERSON>", "zhinao": "360AI", "zhipu": "ZHIPU AI"}, "restore": {"confirm": "Are you sure you want to restore data?", "confirm.button": "Select Backup File", "content": "Restore operation will overwrite all current application data with the backup data. Please note that the restore process may take some time, thank you for your patience.", "progress": {"completed": "Restore completed", "copying_files": "Copying files... {{progress}}%", "extracting": "Extracting backup...", "preparing": "Preparing restore...", "reading_data": "Reading data...", "title": "Restore Progress"}, "title": "Data Restore"}, "selection": {"action": {"builtin": {"copy": "Copy", "explain": "Explain", "quote": "Quote", "refine": "Refine", "search": "Search", "summary": "Summarize", "translate": "Translate"}, "translate": {"smart_translate_tips": "Smart Translation: Content will be translated to the target language first; content already in the target language will be translated to the alternative language"}, "window": {"c_copy": "C: Copy", "esc_close": "Esc: Close", "esc_stop": "Esc: Stop", "opacity": "Window Opacity", "original_copy": "Copy Original", "original_hide": "Hide Original", "original_show": "Show Original", "pin": "<PERSON>n", "pinned": "Pinned", "r_regenerate": "R: Regenerate"}}, "name": "Selection Assistant", "settings": {"actions": {"add_tooltip": {"disabled": "Maximum number of custom actions reached ({{max}})", "enabled": "Add Custom Action"}, "custom": "Custom Action", "delete_confirm": "Are you sure you want to delete this custom action?", "drag_hint": "Drag to reorder. Move above to enable action ({{enabled}}/{{max}})", "reset": {"button": "Reset", "confirm": "Are you sure you want to reset to default actions? Custom actions will not be deleted.", "tooltip": "Reset to default actions. Custom actions will not be deleted."}, "title": "Actions"}, "advanced": {"filter_list": {"description": "Advanced feature, recommended for users with experience", "title": "Filter List"}, "filter_mode": {"blacklist": "Blacklist", "default": "Off", "description": "Can limit the selection assistant to only work in specific applications (whitelist) or not work (blacklist)", "title": "Application Filter", "whitelist": "Whitelist"}, "title": "Advanced"}, "enable": {"description": "Currently only supported on Windows & macOS", "mac_process_trust_hint": {"button": {"go_to_settings": "Go to Settings", "open_accessibility_settings": "Open Accessibility Settings"}, "description": ["Selection Assistant requires <strong>Accessibility Permission</strong> to work properly.", "Please click \"<strong>Go to Settings</strong>\" and click the \"<strong>Open System Settings</strong>\" button in the permission request popup that appears later. Then find \"<strong>Cherry Studio</strong>\" in the application list that appears later and turn on the permission switch.", "After completing the settings, please reopen the selection assistant."], "title": "Accessibility Permission"}, "title": "Enable"}, "experimental": "Experimental Features", "filter_modal": {"title": "Application Filter List", "user_tips": {"mac": "Please enter the Bundle ID of the application, one per line, case insensitive, can be fuzzy matched. For example: com.google.Chrome, com.apple.mail, etc.", "windows": "Please enter the executable file name of the application, one per line, case insensitive, can be fuzzy matched. For example: chrome.exe, weixin.exe, Cherry Studio.exe, etc."}}, "search_modal": {"custom": {"name": {"hint": "Please enter search engine name", "label": "Custom Name", "max_length": "Name cannot exceed 16 characters"}, "test": "Test", "url": {"hint": "Use {{queryString}} to represent the search term", "invalid_format": "Please enter a valid URL starting with http:// or https://", "label": "Custom Search URL", "missing_placeholder": "URL must contain {{queryString}} placeholder", "required": "Please enter search URL"}}, "engine": {"custom": "Custom", "label": "Search Engine"}, "title": "Set Search Engine"}, "toolbar": {"compact_mode": {"description": "In compact mode, only icons are displayed without text", "title": "Compact Mode"}, "title": "<PERSON><PERSON><PERSON>", "trigger_mode": {"ctrlkey": "Ctrl Key", "ctrlkey_note": "After selection, hold down the Ctrl key to show the toolbar", "description": "The way to trigger the selection assistant and show the toolbar", "description_note": {"mac": "If you have remapped the ⌘ key using shortcuts or keyboard mapping tools, it may cause some applications to fail to select text.", "windows": "Some applications do not support selecting text with the Ctrl key. If you have remapped the Ctrl key using tools like AHK, it may cause some applications to fail to select text."}, "selected": "Selection", "selected_note": "Show toolbar immediately when text is selected", "shortcut": "Shortcut", "shortcut_link": "Go to Shortcut Settings", "shortcut_note": "After selection, use shortcut to show the toolbar. Please set the shortcut in the shortcut settings page and enable it. ", "title": "Trigger Mode"}}, "user_modal": {"assistant": {"default": "<PERSON><PERSON><PERSON>", "label": "Select Assistant"}, "icon": {"error": "Invalid icon name, please check your input", "label": "Icon", "placeholder": "Enter Lucide icon name", "random": "Random Icon", "tooltip": "Lucide icon names are lowercase, e.g. arrow-right", "view_all": "View All Icons"}, "model": {"assistant": "Use Assistant", "default": "Default Model", "label": "Model", "tooltip": "Using Assistant: Will use both the assistant's system prompt and model parameters"}, "name": {"hint": "Please enter action name", "label": "Name"}, "prompt": {"copy_placeholder": "Copy Placeholder", "label": "User Prompt", "placeholder": "Use placeholder {{text}} to represent selected text. When empty, selected text will be appended to this prompt", "placeholder_text": "Placeholder", "tooltip": "User prompt serves as a supplement to user input and won't override the assistant's system prompt"}, "title": {"add": "Add Custom Action", "edit": "Edit Custom Action"}}, "window": {"auto_close": {"description": "Automatically close the window when it's not pinned and loses focus", "title": "Auto Close"}, "auto_pin": {"description": "Pin the window by default", "title": "Auto Pin"}, "follow_toolbar": {"description": "Window position will follow the toolbar. When disabled, it will always be centered.", "title": "Follow <PERSON><PERSON><PERSON>"}, "opacity": {"description": "Set the default opacity of the window, 100% is fully opaque", "title": "Opacity"}, "remember_size": {"description": "Window will display at the last adjusted size during the application running", "title": "Remember Size"}, "title": "Action Window"}}}, "settings": {"about": "About & Feedback", "about.checkUpdate": "Check Update", "about.checkUpdate.available": "Update", "about.checkingUpdate": "Checking for updates...", "about.contact.button": "Email", "about.contact.title": "Contact", "about.debug.open": "Open", "about.debug.title": "Debug", "about.description": "A powerful AI assistant for producer", "about.downloading": "Downloading...", "about.feedback.button": "<PERSON><PERSON><PERSON>", "about.feedback.title": "<PERSON><PERSON><PERSON>", "about.license.button": "License", "about.license.title": "License", "about.releases.button": "Releases", "about.releases.title": "Release Notes", "about.social.title": "Social Accounts", "about.title": "About", "about.updateAvailable": "Found new version {{version}}", "about.updateError": "Update error", "about.updateNotAvailable": "You are using the latest version", "about.website.button": "Website", "about.website.title": "Official Website", "advanced.auto_switch_to_topics": "Auto switch to topic", "advanced.title": "Advanced Settings", "assistant": "Default Assistant", "assistant.icon.type": "Model Icon Type", "assistant.icon.type.emoji": "Em<PERSON>ji <PERSON>", "assistant.icon.type.model": "Model Icon", "assistant.icon.type.none": "<PERSON>de", "assistant.model_params": "Model Parameters", "assistant.title": "Default Assistant", "data": {"app_data": "App Data", "app_data.copy_data_option": "Copy data, will automatically restart after copying the original directory data to the new directory", "app_data.copy_failed": "Failed to copy data", "app_data.copy_success": "Successfully copied data to new location", "app_data.copy_time_notice": "Copying data may take a while, do not force quit app", "app_data.copying": "Copying data to new location...", "app_data.copying_warning": "Data copying, do not force quit app, the app will restart after copied", "app_data.migration_title": "Data Migration", "app_data.new_path": "New Path", "app_data.original_path": "Original Path", "app_data.path_changed_without_copy": "Path changed successfully", "app_data.restart_notice": "The app may need to restart multiple times to apply the changes", "app_data.select": "Modify Directory", "app_data.select_error": "Failed to change data directory", "app_data.select_error_in_app_path": "New path is the same as the application installation path, please select another path", "app_data.select_error_root_path": "New path cannot be the root path", "app_data.select_error_same_path": "New path is the same as the old path, please select another path", "app_data.select_error_write_permission": "New path does not have write permission", "app_data.select_not_empty_dir": "New path is not empty", "app_data.select_not_empty_dir_content": "New path is not empty, it will overwrite the data in the new path, there is a risk of data loss and copy failure, continue?", "app_data.select_success": "Data directory changed, the app will restart to apply changes", "app_data.select_title": "Change App Data Directory", "app_data.stop_quit_app_reason": "The app is currently migrating data and cannot be exited", "app_knowledge": "Knowledge Base Files", "app_knowledge.button.delete": "Delete File", "app_knowledge.remove_all": "Remove Knowledge Base Files", "app_knowledge.remove_all_confirm": "Deleting knowledge base files will reduce the storage space occupied, but will not delete the knowledge base vector data, after deletion, the source file will no longer be able to be opened. Continue?", "app_knowledge.remove_all_success": "Files removed successfully", "app_logs": "App Logs", "app_logs.button": "Open Logs", "backup.skip_file_data_help": "<PERSON>p backing up data files such as pictures and knowledge bases during backup, and only back up chat records and settings. Reduce space occupancy and speed up the backup speed.", "backup.skip_file_data_title": "<PERSON>", "clear_cache": {"button": "<PERSON>ache", "confirm": "Clearing the cache will delete application cache data, including minapp data. This action is irreversible, continue?", "error": "Error clearing cache", "success": "<PERSON><PERSON> cleared", "title": "<PERSON>ache"}, "data.title": "Data Directory", "divider.basic": "Basic Data Settings", "divider.cloud_storage": "Cloud Backup Settings", "divider.export_settings": "Export Settings", "divider.third_party": "Third-party Connections", "export_menu": {"docx": "Export as Word", "image": "Export as Image", "joplin": "Export to <PERSON><PERSON><PERSON>", "markdown": "Export as <PERSON><PERSON>", "markdown_reason": "Export as Mark<PERSON> (with reasoning)", "notion": "Export to Notion", "obsidian": "Export to Obsidian", "plain_text": "Copy as Plain Text", "siyuan": "Export to SiYuan Note", "title": "Export Menu <PERSON>s", "yuque": "Export to Yuque"}, "hour_interval_one": "{{count}} hour", "hour_interval_other": "{{count}} hours", "joplin": {"check": {"button": "Check", "empty_token": "Please enter Joplin Authorization Token", "empty_url": "Please enter Joplin Clipper Service URL", "fail": "Joplin connection verification failed", "success": "Joplin connection verification successful"}, "export_reasoning.help": "When enabled, the exported content will include the reasoning chain (thought process) generated by the assistant.", "export_reasoning.title": "Include Reasoning Chain in Export", "help": "In <PERSON><PERSON><PERSON> options, enable the web clipper (no browser extension needed), confirm the port, and copy the auth token here.", "title": "<PERSON><PERSON><PERSON> Configuration", "token": "Joplin Authorization Token", "token_placeholder": "Joplin Authorization Token", "url": "Joplin Web Clipper Service URL", "url_placeholder": "http://127.0.0.1:41184/"}, "local": {"autoSync": "Auto Backup", "autoSync.off": "Off", "backup.button": "Backup to Local", "backup.manager.columns.actions": "Actions", "backup.manager.columns.fileName": "Filename", "backup.manager.columns.modifiedTime": "Modified Time", "backup.manager.columns.size": "Size", "backup.manager.delete.confirm.multiple": "Are you sure you want to delete {{count}} selected backup files? This action cannot be undone.", "backup.manager.delete.confirm.single": "Are you sure you want to delete backup file \"{{fileName}}\"? This action cannot be undone.", "backup.manager.delete.confirm.title": "Confirm Delete", "backup.manager.delete.error": "Delete failed", "backup.manager.delete.selected": "Delete Selected", "backup.manager.delete.success.multiple": "Successfully deleted {{count}} backup files", "backup.manager.delete.success.single": "Deleted successfully", "backup.manager.delete.text": "Delete", "backup.manager.fetch.error": "Failed to get backup files", "backup.manager.refresh": "Refresh", "backup.manager.restore.error": "Rest<PERSON> failed", "backup.manager.restore.success": "Restore successful, application will refresh shortly", "backup.manager.restore.text": "Rest<PERSON>", "backup.manager.select.files.delete": "Please select backup files to delete", "backup.manager.title": "Local Backup Manager", "backup.modal.filename.placeholder": "Please enter backup filename", "backup.modal.title": "Backup to Local Directory", "directory": "Local Backup Directory", "directory.placeholder": "Select a directory for local backups", "directory.select_error_app_data_path": "New path cannot be the same as the application data path", "directory.select_error_in_app_install_path": "New path cannot be the same as the application installation path", "directory.select_error_write_permission": "New path does not have write permission", "directory.select_title": "Select Backup Directory", "hour_interval_one": "{{count}} hour", "hour_interval_other": "{{count}} hours", "lastSync": "Last Backup", "maxBackups": "Maximum backups", "maxBackups.unlimited": "Unlimited", "minute_interval_one": "{{count}} minute", "minute_interval_other": "{{count}} minutes", "noSync": "Waiting for next backup", "restore.button": "<PERSON><PERSON> from Local", "restore.confirm.content": "Restoring from local backup will replace current data. Do you want to continue?", "restore.confirm.title": "Confirm <PERSON>ore", "syncError": "Backup Error", "syncStatus": "Backup Status", "title": "Local Backup"}, "markdown_export.force_dollar_math.help": "When enabled, $$ will be forcibly used to mark LaTeX formulas when exporting to Markdown. Note: This option also affects all export methods through Markdown, such as Notion, Yuque, etc.", "markdown_export.force_dollar_math.title": "Force $$ for LaTeX formulas", "markdown_export.help": "If provided, exports will be automatically saved to this path; otherwise, a save dialog will appear.", "markdown_export.path": "Default Export Path", "markdown_export.path_placeholder": "Export Path", "markdown_export.select": "Select", "markdown_export.show_model_name.help": "When enabled, the model name will be displayed when exporting to Markdown. Note: This option also affects all export methods through Markdown, such as Notion, Yuque, etc.", "markdown_export.show_model_name.title": "Use Model Name on Export", "markdown_export.show_model_provider.help": "Display the model provider (e.g., OpenAI, Gemini) when exporting to Markdown", "markdown_export.show_model_provider.title": "Show Model Provider", "markdown_export.title": "Markdown Export", "message_title.use_topic_naming.help": "When enabled, use topic naming model to create titles for exported messages. This will also affect all Markdown export methods.", "message_title.use_topic_naming.title": "Use topic naming model to create titles for exported messages", "minute_interval_one": "{{count}} minute", "minute_interval_other": "{{count}} minutes", "notion.api_key": "Notion API Key", "notion.api_key_placeholder": "Enter Notion API Key", "notion.check": {"button": "Check", "empty_api_key": "API key is not configured", "empty_database_id": "Database ID is not configured", "error": "Connection error, please check network configuration and API key and Database ID", "fail": "Connection failed, please check network and API key and Database ID", "success": "Connection successful"}, "notion.database_id": "Notion Database ID", "notion.database_id_placeholder": "Enter Notion Database ID", "notion.export_reasoning.help": "When enabled, exported content will include reasoning chain (thought process).", "notion.export_reasoning.title": "Include Reasoning Chain in Export", "notion.help": "Notion Configuration Documentation", "notion.page_name_key": "Page Title Field Name", "notion.page_name_key_placeholder": "Enter page title field name, default is Name", "notion.title": "Notion Settings", "nutstore": {"backup.button": "Backup to Nutstore", "checkConnection.fail": "Nutstore connection failed", "checkConnection.name": "Check Connection", "checkConnection.success": "Connected to Nutstore", "isLogin": "Logged in", "login.button": "<PERSON><PERSON>", "logout.button": "Logout", "logout.content": "After logout, you will not be able to backup to Nutstore or restore from Nutstore.", "logout.title": "Are you sure you want to logout from Nutstore?", "new_folder.button": "New Folder", "new_folder.button.cancel": "Cancel", "new_folder.button.confirm": "Confirm", "notLogin": "Not logged in", "path": "Nutstore Storage Path", "path.placeholder": "Enter Nutstore storage path", "pathSelector.currentPath": "Current Path", "pathSelector.return": "Return", "pathSelector.title": "Nutstore Storage Path", "restore.button": "<PERSON><PERSON> from Nutstore", "title": "Nutstore Configuration", "username": "Nutstore Username"}, "obsidian": {"default_vault": "Default Obsidian Vault", "default_vault_export_failed": "Export failed", "default_vault_fetch_error": "Failed to fetch Obsidian vault", "default_vault_loading": "Loading Obsidian vault...", "default_vault_no_vaults": "No Obsidian vaults found", "default_vault_placeholder": "Please select the default Obsidian vault", "title": "Obsidian Configuration"}, "s3": {"accessKeyId": "Access Key ID", "accessKeyId.placeholder": "Access Key ID", "autoSync": "Auto Sync", "autoSync.hour": "Every {{count}} hour", "autoSync.minute": "Every {{count}} minute", "autoSync.off": "Off", "backup.button": "Backup Now", "backup.error": "S3 backup failed: {{message}}", "backup.manager.button": "Manage Backups", "backup.modal.filename.placeholder": "Please enter backup filename", "backup.modal.title": "S3 Backup", "backup.operation": "Backup Operation", "backup.success": "S3 backup successful", "bucket": "Bucket", "bucket.placeholder": "Bucket, e.g: example", "endpoint": "API Endpoint", "endpoint.placeholder": "https://s3.example.com", "manager.close": "Close", "manager.columns.actions": "Actions", "manager.columns.fileName": "File Name", "manager.columns.modifiedTime": "Modified Time", "manager.columns.size": "File Size", "manager.config.incomplete": "Please fill in complete S3 configuration", "manager.delete": "Delete", "manager.delete.confirm.multiple": "Are you sure you want to delete {{count}} selected backup files? This action cannot be undone.", "manager.delete.confirm.single": "Are you sure you want to delete backup file \"{{fileName}}\"? This action cannot be undone.", "manager.delete.confirm.title": "Confirm Delete", "manager.delete.error": "Failed to delete backup file: {{message}}", "manager.delete.selected": "Delete Selected ({{count}})", "manager.delete.success.multiple": "Successfully deleted {{count}} backup files", "manager.delete.success.single": "Backup file deleted successfully", "manager.files.fetch.error": "Failed to fetch backup file list: {{message}}", "manager.refresh": "Refresh", "manager.restore": "Rest<PERSON>", "manager.select.warning": "Please select backup files to delete", "manager.title": "S3 Backup File Manager", "maxBackups": "Maximum Backups", "maxBackups.unlimited": "Unlimited", "region": "Region", "region.placeholder": "Region, e.g: us-east-1", "restore.config.incomplete": "Please fill in complete S3 configuration", "restore.confirm.cancel": "Cancel", "restore.confirm.content": "Restoring data will overwrite all current data. This action cannot be undone. Are you sure you want to continue?", "restore.confirm.ok": "Confirm <PERSON>ore", "restore.confirm.title": "Confirm Restore Data", "restore.error": "Data restore failed: {{message}}", "restore.file.required": "Please select backup file to restore", "restore.modal.select.placeholder": "Please select backup file to restore", "restore.modal.title": "S3 Data Restore", "restore.success": "Data restore successful", "root": "Backup Directory (Optional)", "root.placeholder": "e.g: /cherry-studio", "secretAccessKey": "Secret Access Key", "secretAccessKey.placeholder": "Secret Access Key", "skipBackupFile": "Lightweight Backup", "skipBackupFile.help": "When enabled, file data will be skipped during backup, only configuration information will be backed up, significantly reducing backup file size", "syncStatus": "Sync Status", "syncStatus.error": "Sync error: {{message}}", "syncStatus.lastSync": "Last sync: {{time}}", "syncStatus.noSync": "Not synced", "title": "S3 Compatible Storage", "title.help": "S3 compatible object storage services, such as AWS S3, Cloudflare R2, Aliyun OSS, Tencent COS, etc.", "title.tooltip": "S3 Compatible Storage Configuration Document"}, "siyuan": {"api_url": "Siyuan Note API URL", "api_url_placeholder": "e.g.: http://127.0.0.1:6806", "box_id": "Siyuan Note Box ID", "box_id_placeholder": "Please enter Siyuan Note Box ID", "check": {"button": "Check", "empty_config": "Please fill in the API address and token", "error": "Connection error, please check network connection", "fail": "Connection failed, please check API address and token", "success": "Connection successful", "title": "Connection Check"}, "root_path": "Siyuan Note Root Path", "root_path_placeholder": "e.g.: /CherryStudio", "title": "Siyuan Note Configuration", "token": "Siyuan Note Token", "token.help": "Get Siyuan Note Token", "token_placeholder": "Please enter Siyuan Note Token"}, "title": "Data Settings", "webdav": {"autoSync": "Auto Backup", "autoSync.off": "Off", "backup.button": "Backup to WebDAV", "backup.manager.columns.actions": "Actions", "backup.manager.columns.fileName": "Filename", "backup.manager.columns.modifiedTime": "Modified Time", "backup.manager.columns.size": "Size", "backup.manager.delete.confirm.multiple": "Are you sure you want to delete {{count}} selected backup files? This action cannot be undone.", "backup.manager.delete.confirm.single": "Are you sure you want to delete backup file \"{{fileName}}\"? This action cannot be undone.", "backup.manager.delete.confirm.title": "Confirm Delete", "backup.manager.delete.error": "Delete failed", "backup.manager.delete.selected": "Delete Selected", "backup.manager.delete.success.multiple": "Successfully deleted {{count}} backup files", "backup.manager.delete.success.single": "Deleted successfully", "backup.manager.delete.text": "Delete", "backup.manager.fetch.error": "Failed to get backup files", "backup.manager.refresh": "Refresh", "backup.manager.restore.error": "Rest<PERSON> failed", "backup.manager.restore.success": "Restore successful, application will refresh shortly", "backup.manager.restore.text": "Rest<PERSON>", "backup.manager.select.files.delete": "Please select backup files to delete", "backup.manager.title": "Backup Data Management", "backup.modal.filename.placeholder": "Please enter backup filename", "backup.modal.title": "Backup to WebDAV", "disableStream": {"help": "When enabled, loads the file into memory before uploading. This can solve incompatibility issues with some WebDAV servers that do not support chunked uploads, but it will increase memory usage.", "title": "Disable Stream Upload"}, "host": "WebDAV Host", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} hour", "hour_interval_other": "{{count}} hours", "lastSync": "Last Backup", "maxBackups": "Maximum Backups", "minute_interval_one": "{{count}} minute", "minute_interval_other": "{{count}} minutes", "noSync": "Waiting for next backup", "password": "WebDAV Password", "path": "WebDAV Path", "path.placeholder": "/backup", "restore.button": "Restore from WebDAV", "restore.confirm.content": "Restoring from WebDAV will overwrite current data. Do you want to continue?", "restore.confirm.title": "Confirm <PERSON>ore", "restore.content": "Restore from WebDAV will overwrite the current data, continue?", "restore.title": "Restore from WebDAV", "syncError": "Backup Error", "syncStatus": "Backup Status", "title": "WebDAV", "user": "WebDAV User"}, "yuque": {"check": {"button": "Check", "empty_repo_url": "Please enter the knowledge base URL first", "empty_token": "Please enter the Yuque Token first", "fail": "Yuque connection verification failed", "success": "Yuque connection verified successfully"}, "help": "Get <PERSON>", "repo_url": "Yuque URL", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Yuque Configuration", "token": "<PERSON><PERSON>", "token_placeholder": "Please enter the Yuque Token"}}, "developer": {"enable_developer_mode": "Enable Developer Mode", "title": "Developer Mode"}, "display.assistant.title": "Assistant Settings", "display.custom.css": "Custom CSS", "display.custom.css.cherrycss": "Get from cherrycss.com", "display.custom.css.placeholder": "/* Put custom CSS here */", "display.navbar.position": "Navbar Position", "display.navbar.position.left": "Left", "display.navbar.position.top": "Top", "display.navbar.title": "<PERSON><PERSON><PERSON>", "display.sidebar.chat.hiddenMessage": "Assistants are basic functions, not supported for hiding", "display.sidebar.disabled": "Hide icons", "display.sidebar.empty": "Drag the hidden feature from the left side here", "display.sidebar.files.icon": "Show Files icon", "display.sidebar.knowledge.icon": "Show Knowledge icon", "display.sidebar.minapp.icon": "Show MinApp icon", "display.sidebar.painting.icon": "Show Painting icon", "display.sidebar.title": "Sidebar Settings", "display.sidebar.translate.icon": "Show Translate icon", "display.sidebar.visible": "Show icons", "display.title": "Display Settings", "display.topic.title": "Topic Settings", "display.zoom.title": "<PERSON>m <PERSON>s", "font_size.title": "Message font size", "general": "General Settings", "general.auto_check_update.title": "Auto Update", "general.avatar.reset": "Reset Avatar", "general.backup.button": "Backup", "general.backup.title": "Data Backup and Recovery", "general.display.title": "Display Settings", "general.emoji_picker": "<PERSON><PERSON><PERSON>", "general.image_upload": "Image Upload", "general.reset.button": "Reset", "general.reset.title": "Data Reset", "general.restore.button": "Rest<PERSON>", "general.spell_check": "Spell Check", "general.spell_check.languages": "Use spell check for", "general.test_plan.beta_version": "Beta Version (Beta)", "general.test_plan.beta_version_tooltip": "Features may change at any time, bugs are more, upgrade quickly", "general.test_plan.rc_version": "Preview Version (RC)", "general.test_plan.rc_version_tooltip": "Close to stable version, features are basically stable, bugs are few", "general.test_plan.title": "Test Plan", "general.test_plan.tooltip": "Participate in the test plan to experience the latest features faster, but also brings more risks, please backup your data in advance", "general.test_plan.version_channel_not_match": "Preview and test version switching will take effect after the next stable version is released", "general.test_plan.version_options": "Version Options", "general.title": "General Settings", "general.user_name": "User Name", "general.user_name.placeholder": "Enter your name", "general.view_webdav_settings": "View WebDAV settings", "hardware_acceleration": {"confirm": {"content": "Disabling hardware acceleration requires restarting the app to take effect. Do you want to restart now?", "title": "<PERSON><PERSON> Required"}, "title": "Disable Hardware Acceleration"}, "input.auto_translate_with_space": "Quickly translate with 3 spaces", "input.show_translate_confirm": "Show translation confirmation dialog", "input.target_language": "Target language", "input.target_language.chinese": "Simplified Chinese", "input.target_language.chinese-traditional": "Traditional Chinese", "input.target_language.english": "English", "input.target_language.japanese": "Japanese", "input.target_language.russian": "Russian", "launch.onboot": "Start Automatically on Boot", "launch.title": "Launch", "launch.totray": "Minimize to Tray on Launch", "mcp": {"actions": "Actions", "active": "Active", "addError": "Failed to add server", "addServer": "Add Server", "addServer.create": "Quick Create", "addServer.importFrom": "Import from JSON", "addServer.importFrom.connectionFailed": "Connection failed", "addServer.importFrom.dxt": "Import DXT Package", "addServer.importFrom.dxtFile": "DXT Package File", "addServer.importFrom.dxtHelp": "Select a .dxt file containing an MCP server package", "addServer.importFrom.dxtProcessFailed": "Failed to process DXT file", "addServer.importFrom.invalid": "Invalid input, please check JSON format", "addServer.importFrom.method": "Import Method", "addServer.importFrom.nameExists": "Server already exists: {{name}}", "addServer.importFrom.noDxtFile": "Please select a DXT file", "addServer.importFrom.oneServer": "Only one MCP server configuration at a time", "addServer.importFrom.placeholder": "Paste MCP server JSON config", "addServer.importFrom.selectDxtFile": "Select DXT File", "addServer.importFrom.tooltip": "Please copy the configuration JSON (prioritizing\n NPX or UVX configurations) from the MCP Servers introduction page and paste it into the input box.", "addSuccess": "Server added successfully", "advancedSettings": "Advanced Settings", "args": "Arguments", "argsTooltip": "Each argument on a new line", "baseUrlTooltip": "Remote server base URL", "builtinServers": "Builtin Servers", "command": "Command", "config_description": "Configure Model Context Protocol servers", "customRegistryPlaceholder": "Enter private registry URL, e.g.: https://npm.company.com", "deleteError": "Failed to delete server", "deleteServer": "Delete Server", "deleteServerConfirm": "Are you sure you want to delete this server?", "deleteSuccess": "Server deleted successfully", "dependenciesInstall": "Install Dependencies", "dependenciesInstalling": "Installing dependencies...", "description": "Description", "disable": "Disable MCP Server", "disable.description": "Do not enable MCP server functionality", "duplicateName": "A server with this name already exists", "editJson": "Edit JSON", "editMcpJson": "Edit MCP Configuration", "editServer": "Edit Server", "env": "Environment Variables", "envTooltip": "Format: KEY=value, one per line", "errors": {"32000": "MCP server failed to start, please check the parameters according to the tutorial", "toolNotFound": "Tool {{name}} not found"}, "findMore": "Find More MCP", "headers": "Headers", "headersTooltip": "Custom headers for HTTP requests", "inMemory": "Memory", "install": "Install", "installError": "Failed to install dependencies", "installHelp": "Get Installation Help", "installSuccess": "Dependencies installed successfully", "jsonFormatError": "JSON formatting error", "jsonModeHint": "Edit the JSON representation of the MCP server configuration. Please ensure the format is correct before saving.", "jsonSaveError": "Failed to save JSON configuration.", "jsonSaveSuccess": "JSON configuration has been saved.", "logoUrl": "Logo URL", "missingDependencies": "is Missing, please install it to continue.", "more": {"awesome": "Curated MCP Server List", "composio": "Composio MCP Development Tools", "glama": "Glama MCP Server Directory", "higress": "Higress MCP Server", "mcpso": "MCP Server Discovery Platform", "modelscope": "ModelScope Community MCP Server", "official": "Official MCP Server Collection", "pulsemcp": "Pulse MCP Server", "smithery": "Smithery MCP Tools"}, "name": "Name", "newServer": "MCP Server", "noDescriptionAvailable": "No description available", "noServers": "No servers configured", "not_support": "Model not supported", "npx_list": {"actions": "Actions", "description": "Description", "no_packages": "No packages found", "npm": "NPM", "package_name": "Package Name", "scope_placeholder": "Enter npm scope (e.g. @your-org)", "scope_required": "Please enter npm scope", "search": "Search", "search_error": "Search error", "usage": "Usage", "version": "Version"}, "prompts": {"arguments": "Arguments", "availablePrompts": "Available Prompts", "genericError": "Get prompt Error", "loadError": "Get prompts Error", "noPromptsAvailable": "No prompts available", "requiredField": "Required Field"}, "provider": "Provider", "providerPlaceholder": "Provider name", "providerUrl": "Provider URL", "registry": "Package Registry", "registryDefault": "<PERSON><PERSON><PERSON>", "registryTooltip": "Choose the registry for package installation to resolve network issues with the default registry.", "requiresConfig": "Requires Configuration", "resources": {"availableResources": "Available Resources", "blob": "Blob", "blobInvisible": "Blob Invisible", "mimeType": "MIME Type", "noResourcesAvailable": "No resources available", "size": "Size", "text": "Text", "uri": "URI"}, "searchNpx": "Search MCP", "serverPlural": "servers", "serverSingular": "server", "sse": "Server-Sent Events (sse)", "startError": "Start failed", "stdio": "Standard Input/Output (stdio)", "streamableHttp": "Streamable HTTP (streamableHttp)", "sync": {"button": "Sync", "discoverMcpServers": "Discover MCP Servers", "discoverMcpServersDescription": "Visit the platform to discover available MCP servers", "error": "Sync MCP Servers error", "getToken": "Get API Token", "getTokenDescription": "Retrieve your personal API token from your account", "noServersAvailable": "No MCP servers available", "selectProvider": "Select Provider:", "setToken": "Enter Your Token", "success": "Sync MCP Servers successful", "title": "Sync Servers", "tokenPlaceholder": "Enter API token here", "tokenRequired": "API Token is required", "unauthorized": "Sync Unauthorized"}, "system": "System", "tabs": {"description": "Description", "general": "General", "prompts": "Prompts", "resources": "Resources", "tools": "Tools"}, "tags": "Tags", "tagsPlaceholder": "Enter tags", "timeout": "Timeout", "timeoutTooltip": "Timeout in seconds for requests to this server, default is 60 seconds", "title": "MCP Settings", "tools": {"autoApprove": "Auto Approve", "autoApprove.tooltip.confirm": "Are you sure you want to run this MCP tool?", "autoApprove.tooltip.disabled": "Tool will require manual approval before running", "autoApprove.tooltip.enabled": "Too<PERSON> will run automatically without confirmation", "autoApprove.tooltip.howToEnable": "Enable the tool first to use auto-approve", "availableTools": "Available Tools", "enable": "Enable Tool", "inputSchema": "Input Schema", "inputSchema.enum.allowedValues": "Allowed Values", "loadError": "Get tools Error", "noToolsAvailable": "No tools available", "run": "Run"}, "type": "Type", "types": {"inMemory": "In Memory", "sse": "SSE", "stdio": "STDIO", "streamableHttp": "Streamable HTTP"}, "updateError": "Failed to update server", "updateSuccess": "Server updated successfully", "url": "URL", "user": "User"}, "messages.divider": "Show divider between messages", "messages.divider.tooltip": "Not applicable to bubble-style message", "messages.grid_columns": "Message grid display columns", "messages.grid_popover_trigger": "Grid detail trigger", "messages.grid_popover_trigger.click": "Click to display", "messages.grid_popover_trigger.hover": "Hover to display", "messages.input.enable_delete_model": "Enable the backspace key to delete models/attachments.", "messages.input.enable_quick_triggers": "Enable / and @ triggers", "messages.input.paste_long_text_as_file": "Paste long text as file", "messages.input.paste_long_text_threshold": "Paste long text length", "messages.input.send_shortcuts": "Send shortcuts", "messages.input.show_estimated_tokens": "Show estimated tokens", "messages.input.title": "Input Settings", "messages.markdown_rendering_input_message": "Markdown render input message", "messages.math_engine": "Math engine", "messages.math_engine.none": "None", "messages.metrics": "{{time_first_token_millsec}}ms to first token | {{token_speed}} tok/sec", "messages.model.title": "Model Settings", "messages.navigation": "Navigation bar", "messages.navigation.anchor": "Message Anchor", "messages.navigation.buttons": "Navigation Buttons", "messages.navigation.none": "None", "messages.prompt": "Show prompt", "messages.title": "Message Settings", "messages.use_serif_font": "Use serif font", "mineru.api_key": "Mineru now offers a daily free quota of 500 pages, and you do not need to enter a key.", "miniapps": {"cache_change_notice": "Changes will take effect when the number of open mini apps reaches the set value", "cache_description": "Set the maximum number of active mini apps to keep in memory", "cache_settings": "<PERSON><PERSON>", "cache_title": "Mini App Cache Limit", "custom": {"conflicting_ids": "Conflicting IDs with default apps: {{ids}}", "duplicate_ids": "Duplicate IDs found: {{ids}}", "edit_description": "Edit custom mini app configuration here. Each app should include id, name, url, and logo fields.", "edit_title": "Edit Custom Mini App", "id": "ID", "id_error": "ID is required.", "id_placeholder": "Enter ID", "logo": "Logo", "logo_file": "Upload Logo File", "logo_upload_button": "Upload", "logo_upload_error": "Failed to upload logo.", "logo_upload_label": "Upload Logo", "logo_upload_success": "Logo uploaded successfully.", "logo_url": "Logo URL", "logo_url_label": "Logo URL", "logo_url_placeholder": "Enter logo URL", "name": "Name", "name_error": "Name is required.", "name_placeholder": "Enter name", "placeholder": "Enter custom mini app configuration (JSON format)", "remove_error": "Failed to remove custom mini app.", "remove_success": "Custom mini app removed successfully.", "save": "Save", "save_error": "Failed to save custom mini app.", "save_success": "Custom mini app saved successfully.", "title": "Custom", "url": "URL", "url_error": "URL is required.", "url_placeholder": "Enter URL"}, "disabled": "Hidden Mini Apps", "display_title": "Mini App Display Settings", "empty": "Drag mini apps from the left to hide them", "open_link_external": {"title": "Open new-window links in browser"}, "reset_tooltip": "Reset to default", "sidebar_description": "Show active mini apps in the sidebar", "sidebar_title": "Sidebar Active Mini Apps Display", "title": "Mini Apps Settings", "visible": "Visible Mini Apps"}, "model": "Default Model", "models.add.add_model": "Add Model", "models.add.batch_add_models": "Batch Add Models", "models.add.endpoint_type": "Endpoint Type", "models.add.endpoint_type.placeholder": "Select endpoint type", "models.add.endpoint_type.required": "Please select an endpoint type", "models.add.endpoint_type.tooltip": "Select the API endpoint type format", "models.add.group_name": "Group Name", "models.add.group_name.placeholder": "Optional e.g. ChatGPT", "models.add.group_name.tooltip": "Optional e.g. ChatGPT", "models.add.model_id": "Model ID", "models.add.model_id.placeholder": "Required e.g. gpt-3.5-turbo", "models.add.model_id.select.placeholder": "Select Model", "models.add.model_id.tooltip": "Example: gpt-3.5-turbo", "models.add.model_name": "Model Name", "models.add.model_name.placeholder": "Optional e.g. GPT-4", "models.add.model_name.tooltip": "Optional e.g. GPT-4", "models.api_key": "API Key", "models.base_url": "Base URL", "models.check.all": "All", "models.check.all_models_passed": "All models check passed", "models.check.button_caption": "Health check", "models.check.disabled": "Disabled", "models.check.disclaimer": "Health check requires sending requests, please use it with caution. Models that charge per request may incur additional costs, please bear the responsibility.", "models.check.enable_concurrent": "Concurrent", "models.check.enabled": "Enabled", "models.check.failed": "Failed", "models.check.keys_status_count": "Passed: {{count_passed}} keys, failed: {{count_failed}} keys", "models.check.model_status_failed": "{{count}} models completely inaccessible", "models.check.model_status_partial": "{{count}} models had inaccessible keys", "models.check.model_status_passed": "{{count}} models passed health checks", "models.check.model_status_summary": "{{provider}}: {{summary}}", "models.check.no_api_keys": "No API keys found, please add API keys first.", "models.check.passed": "Passed", "models.check.select_api_key": "Select the API key to use:", "models.check.single": "Single", "models.check.start": "Start", "models.check.title": "Model health check", "models.check.use_all_keys": "Key(s)", "models.default_assistant_model": "De<PERSON>ult Assistant Model", "models.default_assistant_model_description": "Model used when creating a new assistant, if the assistant is not set, this model will be used", "models.empty": "No models found", "models.enable_topic_naming": "Topic Auto Naming", "models.manage.add_listed": "Add models to the list", "models.manage.add_whole_group": "Add the whole group", "models.manage.remove_listed": "Remove models from the list", "models.manage.remove_model": "Remove model", "models.manage.remove_whole_group": "Remove the whole group", "models.provider_id": "Provider ID", "models.provider_key_add_confirm": "Do you want to add the API key for {{provider}}?", "models.provider_key_add_failed_by_empty_data": "Failed to add provider API key, data is empty", "models.provider_key_add_failed_by_invalid_data": "Failed to add provider API key, data format error", "models.provider_key_added": "Successfully added API key for {{provider}}", "models.provider_key_already_exists": "{{provider}} already has an API key ({{existingKey}}). Do not add it again.", "models.provider_key_confirm_title": "Add Provider API Key", "models.provider_key_no_change": "API key for {{provider}} has not changed", "models.provider_key_overridden": "Successfully updated API key for {{provider}}", "models.provider_key_override_confirm": "{{provider}} already has an API key ({{existingKey}}). Do you want to override it with the new key ({{newKey}})?", "models.provider_name": "Provider Name", "models.quick_assistant_default_tag": "<PERSON><PERSON><PERSON>", "models.quick_assistant_model": "Quick Assistant Model", "models.quick_assistant_model_description": "Default model used by Quick Assistant", "models.quick_assistant_selection": "Select Assistant", "models.topic_naming_model": "Topic Naming Model", "models.topic_naming_model_description": "Model used when automatically naming a new topic", "models.topic_naming_model_setting_title": "Topic Naming Model Settings", "models.topic_naming_prompt": "Topic Naming Prompt", "models.translate_model": "Translate Model", "models.translate_model_description": "Model used for translation service", "models.translate_model_prompt_message": "Please enter the translate model prompt", "models.translate_model_prompt_title": "Translate Model Prompt", "models.use_assistant": "Use Assistant", "models.use_model": "Default Model", "moresetting": "More Settings", "moresetting.check.confirm": "Confirm Selection", "moresetting.check.warn": "Please be cautious when selecting this option. Incorrect selection may cause the model to malfunction!", "moresetting.warn": "Risk Warning", "notification": {"assistant": "Assistant Message", "backup": "Backup Message", "knowledge_embed": "KnowledgeBase Message", "title": "Notification Settings"}, "openai": {"service_tier.auto": "auto", "service_tier.default": "default", "service_tier.flex": "flex", "service_tier.tip": "Specifies the latency tier to use for processing the request", "service_tier.title": "Service Tier", "summary_text_mode.auto": "auto", "summary_text_mode.concise": "concise", "summary_text_mode.detailed": "detailed", "summary_text_mode.off": "off", "summary_text_mode.tip": "A summary of the reasoning performed by the model", "summary_text_mode.title": "Summary Mode", "title": "OpenAI Settings"}, "privacy": {"enable_privacy_mode": "Anonymous reporting of errors and statistics", "title": "Privacy Settings"}, "provider": {"add.name": "Provider Name", "add.name.placeholder": "Example: OpenAI", "add.title": "Add Provider", "add.type": "Provider Type", "api.key.check.latency": "Latency", "api.key.error.duplicate": "API key already exists", "api.key.error.empty": "API key cannot be empty", "api.key.list.open": "Open Management Interface", "api.key.list.title": "API Key Management", "api.key.new_key.placeholder": "Enter one or more keys", "api.url.preview": "Preview: {{url}}", "api.url.reset": "Reset", "api.url.tip": "Ending with / ignores v1, ending with # forces use of input address", "api_host": "API Host", "api_key": "API Key", "api_key.tip": "Multiple keys separated by commas or spaces", "api_version": "API Version", "azure.apiversion.tip": "The API version of Azure OpenAI, if you want to use Response API, please enter the preview version", "basic_auth": "HTTP authentication", "basic_auth.password": "Password", "basic_auth.password.tip": "", "basic_auth.tip": "Applicable to instances deployed remotely (see the documentation). Currently, only the Basic scheme (RFC 7617) is supported.", "basic_auth.user_name": "Username", "basic_auth.user_name.tip": "Left empty to disable", "bills": "Fee Bills", "charge": "Balance Recharge", "check": "Check", "check_all_keys": "Check All Keys", "check_multiple_keys": "Check Multiple API Keys", "copilot": {"auth_failed": "Github Copilot authentication failed.", "auth_success": "GitHub Copilot authentication successful.", "auth_success_title": "Certification successful.", "code_copied": "Authorization code automatically copied to clipboard", "code_failed": "Failed to obtain Device Code, please try again.", "code_generated_desc": "Please copy the device code into the browser link below.", "code_generated_title": "Obtain Device Code", "connect": "Connect to Github", "custom_headers": "Custom request header", "description": "Your GitHub account needs to subscribe to Copilot.", "description_detail": "GitHub Copilot is an AI-powered code assistant that requires a valid GitHub Copilot subscription to use", "expand": "Expand", "headers_description": "Custom request headers (JSON format)", "invalid_json": "JSON format error", "login": "Log in to Github", "logout": "Exit GitHub", "logout_failed": "Exit failed, please try again.", "logout_success": "Successfully logged out.", "model_setting": "Model settings", "open_verification_first": "Please click the link above to access the verification page.", "open_verification_page": "Open Authorization Page", "rate_limit": "Rate limiting", "start_auth": "Start Authorization", "step_authorize": "Open Authorization Page", "step_authorize_desc": "Complete authorization on GitHub", "step_authorize_detail": "Click the button below to open GitHub authorization page, then enter the copied authorization code", "step_connect": "Complete Connection", "step_connect_desc": "Confirm connection to GitHub", "step_connect_detail": "After completing authorization on GitHub page, click this button to complete the connection", "step_copy_code": "Copy Authorization Code", "step_copy_code_desc": "Copy device authorization code", "step_copy_code_detail": "Authorization code has been automatically copied, you can also copy it manually", "step_get_code": "Get Authorization Code", "step_get_code_desc": "Generate device authorization code"}, "delete.content": "Are you sure you want to delete this provider?", "delete.title": "Delete Provider", "dmxapi": {"select_platform": "Select the platform"}, "docs_check": "Check", "docs_more_details": "for more details", "get_api_key": "Get API Key", "is_not_support_array_content": "Enable compatible mode", "no_models_for_check": "No models available for checking (e.g. chat models)", "not_checked": "Not Checked", "notes": {"markdown_editor_default_value": "Preview area", "placeholder": "Enter Markdown content...", "title": "Model Notes"}, "oauth": {"button": "Login with {{provider}}", "description": "This service is provided by <website>{{provider}}</website>", "official_website": "Official Website"}, "openai": {"alert": "OpenAI Provider no longer support the old calling methods. If using a third-party API, please create a new service provider."}, "remove_duplicate_keys": "Remove <PERSON>", "remove_invalid_keys": "Remove Invalid Keys", "search": "Search Providers...", "search_placeholder": "Search model id or name", "title": "Model Provider", "vertex_ai": {"documentation": "View official documentation for more configuration details:", "learn_more": "Learn More", "location": "Location", "location_help": "Vertex AI service location, e.g., us-central1", "project_id": "Project ID", "project_id_help": "Your Google Cloud project ID", "project_id_placeholder": "your-google-cloud-project-id", "service_account": {"auth_success": "Service Account authenticated successfully", "client_email": "Client Email", "client_email_help": "The client_email field from the JSON key file downloaded from Google Cloud Console", "client_email_placeholder": "Enter Service Account client email", "description": "Use Service Account for authentication, suitable for environments where ADC is not available", "incomplete_config": "Please complete Service Account configuration first", "private_key": "Private Key", "private_key_help": "The private_key field from the JSON key file downloaded from Google Cloud Console", "private_key_placeholder": "Enter Service Account private key", "title": "Service Account Configuration"}}}, "proxy": {"address": "Proxy Address", "mode": {"custom": "Custom Proxy", "none": "No Proxy", "system": "System Proxy", "title": "Proxy Mode"}}, "quickAssistant": {"click_tray_to_show": "Click the tray icon to start", "enable_quick_assistant": "Enable Quick Assistant", "read_clipboard_at_startup": "Read clipboard at startup", "title": "Quick Assistant", "use_shortcut_to_show": "Right-click the tray icon or use shortcuts to start"}, "quickPanel": {"back": "Back", "close": "Close", "confirm": "Confirm", "forward": "Forward", "multiple": "Multiple Select", "page": "Page", "select": "Select", "title": "Quick Menu"}, "quickPhrase": {"add": "Add Phrase", "assistant": "Assistant Phrases", "contentLabel": "Content", "contentPlaceholder": "Please enter phrase content, support using variables, and press Tab to quickly locate the variable to modify. For example: \nHelp me plan a route from ${from} to ${to}, and send it to ${email}.", "delete": "Delete Phrase", "deleteConfirm": "The phrase cannot be recovered after deletion, continue?", "edit": "Edit Phrase", "global": "Global Phrases", "locationLabel": "Add Location", "title": "Quick Phrases", "titleLabel": "Title", "titlePlaceholder": "Please enter phrase title"}, "shortcuts": {"action": "Action", "clear_shortcut": "Clear Shortcut", "clear_topic": "Clear Messages", "copy_last_message": "Copy Last Message", "exit_fullscreen": "Exit Fullscreen", "key": "Key", "mini_window": "Quick Assistant", "new_topic": "New Topic", "press_shortcut": "Press Shortcut", "reset_defaults": "<PERSON><PERSON>s", "reset_defaults_confirm": "Are you sure you want to reset all shortcuts?", "reset_to_default": "Reset to De<PERSON>ult", "search_message": "Search Message", "search_message_in_chat": "Search Message in Current Chat", "selection_assistant_select_text": "Selection Assistant: Select Text", "selection_assistant_toggle": "Toggle Selection Assistant", "show_app": "Show/Hide App", "show_settings": "Open Settings", "title": "Keyboard Shortcuts", "toggle_new_context": "Clear Context", "toggle_show_assistants": "Toggle Assistants", "toggle_show_topics": "Toggle Topics", "zoom_in": "Zoom In", "zoom_out": "Zoom Out", "zoom_reset": "Reset Zoom"}, "theme.color_primary": "Primary Color", "theme.dark": "Dark", "theme.light": "Light", "theme.system": "System", "theme.title": "Theme", "theme.window.style.opaque": "Opaque Window", "theme.window.style.title": "Window Style", "theme.window.style.transparent": "Transparent Window", "title": "Settings", "tool": {"ocr": {"mac_system_ocr_options": {"min_confidence": "Minimum Confidence", "mode": {"accurate": "Accurate", "fast": "Fast", "title": "Recognition Mode"}}, "provider": "OCR Provider", "provider_placeholder": "Choose an OCR provider", "title": "OCR Settings"}, "preprocess": {"provider": "Pre Process Provider", "provider_placeholder": "Choose a Pre Process provider", "title": "Pre Process"}, "preprocessOrOcr.tooltip": "In Settings -> Tools, set a document preprocessing service provider or OCR. Document preprocessing can effectively improve the retrieval performance of complex format documents and scanned documents. OCR can only recognize text within images in documents or scanned PDF text.", "title": "Tools Settings", "websearch": {"apikey": "API key", "blacklist": "Blacklist", "blacklist_description": "Results from the following websites will not appear in search results", "blacklist_tooltip": "Please use the following format (separated by newlines)\nPattern matching: *://*.example.com/*\nRegular expression: /example\\.(net|org)/", "check": "Check", "check_failed": "Verification failed", "check_success": "Verification successful", "compression": {"cutoff.limit": "Cutoff Limit", "cutoff.limit.placeholder": "Enter length", "cutoff.limit.tooltip": "Limit the content length of search results, content exceeding the limit will be truncated (e.g., 2000 characters)", "cutoff.unit.char": "Char", "cutoff.unit.token": "Token", "error": {"dimensions_auto_failed": "Failed to auto-obtain dimensions", "embedding_model_required": "Please select an embedding model first", "provider_not_found": "Provider not found", "rag_failed": "RAG failed"}, "info": {"dimensions_auto_success": "Dimensions auto-obtained successfully, dimensions: {{dimensions}}"}, "method": "Compression Method", "method.cutoff": "Cutoff", "method.none": "None", "method.rag": "RAG", "rag.document_count": "Document Chunks Count", "rag.document_count.tooltip": "Expected number of document chunks to extract from each search result, the actual total number of extracted document chunks is this value multiplied by the number of search results.", "rag.embedding_dimensions.auto_get": "Auto Get Dimensions", "rag.embedding_dimensions.placeholder": "Leave empty", "rag.embedding_dimensions.tooltip": "If left blank, the dimensions parameter will not be passed", "title": "Search Result Compression"}, "content_limit": "Content length limit", "content_limit_tooltip": "Limit the content length of the search results; content that exceeds the limit will be truncated.", "free": "Free", "no_provider_selected": "Please select a search service provider before checking.", "overwrite": "Override search service", "overwrite_tooltip": "Force use search service instead of LLM", "search_max_result": "Number of search results", "search_max_result.tooltip": "When search result compression is disabled, the number of results may be too large, which may lead to insufficient tokens", "search_provider": "Search service provider", "search_provider_placeholder": "Choose a search service provider.", "search_with_time": "Search with dates included", "subscribe": "Blacklist Subscription", "subscribe_add": "Add Subscription", "subscribe_add_success": "Subscription feed added successfully!", "subscribe_delete": "Delete", "subscribe_name": "Alternative name", "subscribe_name.placeholder": "Alternative name used when the downloaded subscription feed has no name.", "subscribe_update": "Update", "subscribe_url": "Subscription Url", "tavily": {"api_key": "Tavily API Key", "api_key.placeholder": "Enter Tavily API Key", "description": "Tavily is a search engine tailored for AI agents, delivering real-time, accurate results, intelligent query suggestions, and in-depth research capabilities.", "title": "<PERSON><PERSON>"}, "title": "Web Search"}}, "topic.pin_to_top": "<PERSON>n <PERSON> to Top", "topic.position": "Topic position", "topic.position.left": "Left", "topic.position.right": "Right", "topic.show.time": "Show topic time", "tray.onclose": "Minimize to Tray on Close", "tray.show": "Show Tray Icon", "tray.title": "Tray", "zoom": {"reset": "Reset", "title": "Page Zoom"}}, "title": {"agents": "Agents", "apps": "Apps", "files": "Files", "home": "Home", "knowledge": "Knowledge Base", "launchpad": "Launchpad", "mcp-servers": "MCP Servers", "memories": "Memories", "paintings": "Paintings", "settings": "Settings", "translate": "Translate"}, "trace": {"backList": "Back To List", "edasSupport": "Powered by Alibaba Cloud EDAS", "endTime": "End Time", "inputs": "Inputs", "label": "Call Chain", "name": "Node Name", "noTraceList": "No trace information found", "outputs": "Outputs", "parentId": "Parent Id", "spanDetail": "Span Details", "spendTime": "Spend Time", "startTime": "Start Time", "tag": "Tag", "tokenUsage": "Token Usage", "traceWindow": "Call Chain Window"}, "translate": {"alter_language": "Alternative Language", "any.language": "Any language", "button.translate": "Translate", "close": "Close", "closed": "Translation closed", "confirm": {"content": "Translation will replace the original text, continue?", "title": "Translation Confirmation"}, "copied": "Translation content copied", "detected.language": "Auto Detect", "empty": "Translation content is empty", "error.failed": "Translation failed", "error.not_configured": "Translation model is not configured", "history": {"clear": "Clear History", "clear_description": "Clear history will delete all translation history, continue?", "delete": "Delete", "empty": "No translation history", "title": "Translation History"}, "input.placeholder": "Enter text to translate", "language.not_pair": "Source language is different from the set language", "language.same": "Source and target languages are the same", "menu": {"description": "Translate the content of the current input box"}, "not.found": "Translation content not found", "output.placeholder": "Translation", "processing": "Translation in progress...", "settings": {"bidirectional": "Bidirectional Translation Settings", "bidirectional_tip": "When enabled, only bidirectional translation between source and target languages is supported", "model": "Model Settings", "model_desc": "Model used for translation service", "preview": "Markdown Preview", "scroll_sync": "<PERSON>roll Sync Settings", "title": "Translation Settings"}, "target_language": "Target Language", "title": "Translation", "tooltip.newline": "Newline"}, "tray": {"quit": "Quit", "show_mini_window": "Quick Assistant", "show_window": "Show Window"}, "update": {"install": "Install", "later": "Later", "message": "New version {{version}} is ready, do you want to install it now?", "noReleaseNotes": "No release notes", "title": "Update"}, "words": {"knowledgeGraph": "Knowledge Graph", "quit": "Quit", "show_window": "Show Window", "visualization": "Visualization"}}}