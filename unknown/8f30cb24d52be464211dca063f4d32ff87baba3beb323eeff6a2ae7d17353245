name: 🤔 其他问题 (中文)
description: 提交不属于错误报告或功能需求的问题
title: '[其他]: '
body:
  - type: markdown
    attributes:
      value: |
        感谢您花时间提出问题！
        在提交此问题之前，请确保您已经了解了[常见问题](https://docs.cherry-ai.com/question-contact/questions)和[知识科普](https://docs.cherry-ai.com/question-contact/knowledge)

  - type: checkboxes
    id: checklist
    attributes:
      label: 提交前检查
      description: |
        在提交 Issue 前请确保您已经完成了以下所有步骤
      options:
        - label: 我理解 Issue 是用于反馈和解决问题的，而非吐槽评论区，将尽可能提供更多信息帮助问题解决。
          required: true
        - label: 我已经查看了置顶 Issue 并搜索了现有的 [开放Issue](https://github.com/CherryHQ/cherry-studio/issues)和[已关闭Issue](https://github.com/CherryHQ/cherry-studio/issues?q=is%3Aissue%20state%3Aclosed%20)，没有找到类似的问题。
          required: true
        - label: 我填写了简短且清晰明确的标题，以便开发者在翻阅 Issue 列表时能快速确定大致问题。而不是"一个问题"、"求助"等。
          required: true
        - label: 我的问题不属于错误报告或功能需求类别。
          required: true

  - type: dropdown
    id: platform
    attributes:
      label: 平台
      description: 您正在使用哪个平台？
      options:
        - Windows
        - macOS
        - Linux
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: 版本
      description: 您正在运行的 Cherry Studio 版本是什么？
      placeholder: 例如 v1.0.0
    validations:
      required: true

  - type: textarea
    id: question
    attributes:
      label: 问题描述
      description: 请详细描述您的问题或疑问
      placeholder: 我想了解有关...的更多信息
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: 相关背景
      description: 请提供与您的问题相关的任何背景信息或上下文
      placeholder: 我尝试实现...时遇到了疑问
    validations:
      required: true

  - type: textarea
    id: attempts
    attributes:
      label: 您已尝试的方法
      description: 请描述您为解决问题已经尝试过的方法（如果有）

  - type: textarea
    id: additional
    attributes:
      label: 附加信息
      description: 任何能让我们对您的问题有更多了解的信息，包括截图或相关链接
